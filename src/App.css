.App {
  .layout {
    display: flex;
    height: 100vh;
    width: 100vw;

    .left_wrapper {
      flex-grow: 0;
    }

    .right_wrapper {
      height: 100vh;
      flex-grow: 1;
      display: flex;
      overflow: hidden;
      flex-wrap: nowrap;
      align-items: stretch;
      flex-direction: column;
      justify-content: flex-start;

      .body_wrapper {
        flex-grow: 1;
        display: flex;
        align-self: auto;
        overflow: hidden;
      }
    }
  }

  .override-hero {
    .hero-banner-content-container {
      .mldc-container-box {
        margin-left: 15px !important;
      }
    }
  }
}

.container {
  flex: 1;
  display: flex;
  margin: 0 auto;
  flex-wrap: nowrap;
  flex-direction: column;
  justify-content: flex-start;
  max-width: 92vw !important;
}

.title {
  align-self: center;
  font-size: 2rem;
  margin-bottom: 1rem;
}

.loading-wrapper {
  width: -webkit-fill-available;
}
