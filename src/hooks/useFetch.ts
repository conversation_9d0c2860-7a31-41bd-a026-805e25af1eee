import { useCallback, useEffect, useState } from "react";

type UseFetchResponse<T> = {
  result: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => void;
};

type GetProps = {
  method: "GET" | "HEAD";
  data?: never;
};

type PostProps = {
  method?: "POST" | "DELETE" | "PUT";
  data?: any;
};

type OptionsProps = GetProps | PostProps;

type BaseProps = { url: string; call: boolean };

type UseFetchProps = BaseProps & OptionsProps;

export type UsePostProps = BaseProps & PostProps;

export function useFetch<T>({
  url,
  call,
  data,
  method,
}: UseFetchProps): UseFetchResponse<T> {
  const [loading, setLoading] = useState(call);
  const [result, setResult] = useState<T | null>(null);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    setError(null);
    setLoading(true);
    try {
      const headers = data ? { "Content-type": "application/json" } : undefined;
      const response = await fetch(url, {
        headers,
        method,
        body: data ? JSON.stringify(data) : undefined,
      });
      const json: any = response.json;
      setResult(json);
    } catch (error) {
      setError(error as Error);
    } finally {
      setLoading(false);
    }
  }, [url, data, method]);

  useEffect(() => {
    if (call) fetchData();
  }, [call, fetchData]);

  return { result, loading, error, refetch: fetchData };
}

export function useGet<T>({ url, call }: BaseProps): UseFetchResponse<T> {
  return useFetch({ url, call, method: "GET" });
}

export function usePost<T>({
  url,
  call,
  data,
  method = "POST",
}: UsePostProps): UseFetchResponse<T> {
  return useFetch({ url, call, method, data });
}

export const initialPostApi = {
  url: "",
  call: false,
  data: undefined,
  method: undefined,
};
