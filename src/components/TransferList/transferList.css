.transfer-list-container {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  margin-top: 20px;
  height: 500px;
}

.transfer-list-box {
  flex: 1;
  border: 1px solid #ccc;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  max-width: 45%;
}

.transfer-list-title {
  padding: 10px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ccc;
  margin: 0;
  font-size: 1rem;
  text-align: center;
}

.transfer-list-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.transfer-list-items {
  list-style-type: none;
  padding-left: 0;
  margin: 0;
}

.transfer-list-items ul {
  padding-left: 20px;
}

.transfer-list-item {
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 4px;
}

.transfer-list-item:hover {
  background-color: #f0f0f0;
}

.transfer-list-item.selected {
  background-color: #e0e0ff;
}

.item-content {
  display: flex;
  align-items: center;
}

.transfer-buttons {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 20px;
}

.transfer-button {
  margin: 5px 0;
  padding: 8px;
  background-color: #0061a0;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.transfer-button:hover:not(:disabled) {
  background-color: #004d80;
}

.transfer-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.empty-list {
  color: #999;
  text-align: center;
  padding: 20px;
}

.title {
  font-size: 2rem;
  margin-bottom: 20px;
  text-align: center;
}
