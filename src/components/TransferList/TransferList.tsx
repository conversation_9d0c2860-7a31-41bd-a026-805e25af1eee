import { FC, useState, useEffect } from "react";
import "./transferList.css";

// Define the structure for tree items
interface TreeItem {
  id: string;
  name: string;
  roleKey?: string;
  module?: string;
  descripcion?: string;
  children?: TreeItem[];
}

// Props for the TransferList component
interface TransferListProps {
  leftTitle?: string;
  rightTitle?: string;
  roleKeyFilter?: string;
  moduleFilter?: string;
}

const TransferList: FC<TransferListProps> = ({
  leftTitle = "Lista Izquierda",
  rightTitle = "Lista Derecha",
  roleKeyFilter = "",
  moduleFilter = "",
}) => {
  // Sample initial data for left list (available menus)
  const initialLeftItems: TreeItem[] = [
    {
      id: "1",
      name: "Gestión de Ingresos",
      roleKey: "admin",
      module: "I",
      children: [
        { id: "1-1", name: "Reportes de Ingresos", roleKey: "admin", module: "I" },
        { id: "1-2", name: "<PERSON><PERSON><PERSON><PERSON>", roleKey: "contador", module: "I" },
        { id: "1-3", name: "Presupuesto de Ingresos", roleKey: "supervisor", module: "I" },
      ],
    },
    {
      id: "2",
      name: "Operaciones de Ingresos",
      roleKey: "cajero",
      module: "I",
      children: [
        { id: "2-1", name: "Registro de Cobros", roleKey: "cajero", module: "I" },
        { id: "2-2", name: "Consultas de Ingresos", roleKey: "cajero", module: "I" },
        { id: "2-3", name: "Conciliación de Ingresos", roleKey: "supervisor", module: "I" },
      ],
    },
    {
      id: "3",
      name: "Gestión de Egresos",
      roleKey: "admin",
      module: "E",
      children: [
        { id: "3-1", name: "Reportes de Egresos", roleKey: "admin", module: "E" },
        { id: "3-2", name: "Control de Gastos", roleKey: "contador", module: "E" },
        { id: "3-3", name: "Presupuesto de Egresos", roleKey: "supervisor", module: "E" },
      ],
    },
    {
      id: "4",
      name: "Operaciones de Egresos",
      roleKey: "cajero",
      module: "E",
      children: [
        { id: "4-1", name: "Registro de Pagos", roleKey: "cajero", module: "E" },
        { id: "4-2", name: "Consultas de Egresos", roleKey: "cajero", module: "E" },
        { id: "4-3", name: "Autorización de Pagos", roleKey: "supervisor", module: "E" },
      ],
    },
  ];

  // Function to get initial right items based on filters
  const getInitialRightItems = (module: string, role: string): TreeItem[] => {
    // Complete sample assignments data structure
    const allAssignments: TreeItem[] = [
      // Ingresos - Admin
      {
        id: "assigned-i-admin-1",
        name: "Administración Completa - Ingresos",
        roleKey: "admin",
        module: "I",
        children: [
          { id: "assigned-i-admin-1-1", name: "Mantenimiento de Compañías", roleKey: "admin", module: "I" },
          { id: "assigned-i-admin-1-2", name: "Mantenimiento de Usuarios", roleKey: "admin", module: "I" },
          { id: "assigned-i-admin-1-3", name: "Configuración de Ingresos", roleKey: "admin", module: "I" },
        ],
      },
      // Ingresos - Cajero
      {
        id: "assigned-i-cajero-1",
        name: "Operaciones Básicas - Ingresos",
        roleKey: "cajero",
        module: "I",
        children: [
          { id: "assigned-i-cajero-1-1", name: "Registro de Cobros", roleKey: "cajero", module: "I" },
          { id: "assigned-i-cajero-1-2", name: "Consultas de Ingresos", roleKey: "cajero", module: "I" },
        ],
      },
      // Ingresos - Supervisor
      {
        id: "assigned-i-supervisor-1",
        name: "Supervisión de Ingresos",
        roleKey: "supervisor",
        module: "I",
        children: [
          { id: "assigned-i-supervisor-1-1", name: "Autorización de Ingresos", roleKey: "supervisor", module: "I" },
          { id: "assigned-i-supervisor-1-2", name: "Reportes de Supervisión", roleKey: "supervisor", module: "I" },
        ],
      },
      // Egresos - Admin
      {
        id: "assigned-e-admin-1",
        name: "Administración Completa - Egresos",
        roleKey: "admin",
        module: "E",
        children: [
          { id: "assigned-e-admin-1-1", name: "Mantenimiento de Proveedores", roleKey: "admin", module: "E" },
          { id: "assigned-e-admin-1-2", name: "Mantenimiento de Conceptos de Pago", roleKey: "admin", module: "E" },
          { id: "assigned-e-admin-1-3", name: "Configuración de Egresos", roleKey: "admin", module: "E" },
        ],
      },
      // Egresos - Cajero
      {
        id: "assigned-e-cajero-1",
        name: "Operaciones Básicas - Egresos",
        roleKey: "cajero",
        module: "E",
        children: [
          { id: "assigned-e-cajero-1-1", name: "Registro de Pagos", roleKey: "cajero", module: "E" },
          { id: "assigned-e-cajero-1-2", name: "Consultas de Egresos", roleKey: "cajero", module: "E" },
        ],
      },
      // Egresos - Contador
      {
        id: "assigned-e-contador-1",
        name: "Contabilidad de Egresos",
        roleKey: "contador",
        module: "E",
        children: [
          { id: "assigned-e-contador-1-1", name: "Conciliación de Pagos", roleKey: "contador", module: "E" },
          { id: "assigned-e-contador-1-2", name: "Reportes Contables", roleKey: "contador", module: "E" },
        ],
      },
    ];

    // If no filters are applied, return all items
    if ((!module || module === "") && (!role || role === "")) {
      return allAssignments;
    }

    // Filter by module and/or role
    return allAssignments.filter(item => {
      const moduleMatch = !module || module === "" || item.module === module;
      const roleMatch = !role || role === "" || item.roleKey === role;
      return moduleMatch && roleMatch;
    });
  };

  // Sample initial data for right list (assigned menus)
  const initialRightItems: TreeItem[] = getInitialRightItems(moduleFilter, roleKeyFilter);

  // State for both lists
  const [leftItems, setLeftItems] = useState<TreeItem[]>(initialLeftItems);
  const [rightItems, setRightItems] = useState<TreeItem[]>(initialRightItems);

  // State for selected items
  const [selectedLeftItem, setSelectedLeftItem] = useState<{item: TreeItem | null, path: string[]}>({item: null, path: []});
  const [selectedRightItem, setSelectedRightItem] = useState<{item: TreeItem | null, path: string[]}>({item: null, path: []});

  // Update right items when filters change
  useEffect(() => {
    const newRightItems = getInitialRightItems(moduleFilter, roleKeyFilter);
    setRightItems(newRightItems);
    // Clear selections when filters change
    setSelectedRightItem({item: null, path: []});
  }, [moduleFilter, roleKeyFilter]);



  // Helper function to find an item by path
  const findItemByPath = (items: TreeItem[], path: string[]): TreeItem | null => {
    if (path.length === 0) return null;

    const currentId = path[0];
    const item = items.find(item => item.id === currentId);

    if (!item) return null;

    if (path.length === 1) return item;

    if (!item.children) return null;

    return findItemByPath(item.children, path.slice(1));
  };

  // Helper function to remove an item by path
  const removeItemByPath = (items: TreeItem[], path: string[]): TreeItem[] => {
    if (path.length === 0) return items;

    const currentId = path[0];

    if (path.length === 1) {
      return items.filter(item => item.id !== currentId);
    }

    const itemIndex = items.findIndex(item => item.id === currentId);

    if (itemIndex === -1 || !items[itemIndex].children) return items;

    const updatedItems = [...items];
    updatedItems[itemIndex] = {
      ...updatedItems[itemIndex],
      children: removeItemByPath(updatedItems[itemIndex].children || [], path.slice(1))
    };

    return updatedItems;
  };

  // Helper function to add an item to a specific parent
  const addItemToParent = (items: TreeItem[], parentPath: string[], newItem: TreeItem): TreeItem[] => {
    if (parentPath.length === 0) {
      return [...items, newItem];
    }

    const currentId = parentPath[0];
    const itemIndex = items.findIndex(item => item.id === currentId);

    if (itemIndex === -1) return items;

    const updatedItems = [...items];

    if (parentPath.length === 1) {
      // Add to this parent's children
      updatedItems[itemIndex] = {
        ...updatedItems[itemIndex],
        children: [...(updatedItems[itemIndex].children || []), newItem]
      };
    } else {
      // Continue traversing
      updatedItems[itemIndex] = {
        ...updatedItems[itemIndex],
        children: addItemToParent(updatedItems[itemIndex].children || [], parentPath.slice(1), newItem)
      };
    }

    return updatedItems;
  };

  // Move selected item from left to right
  const moveSelectedToRight = () => {
    if (!selectedLeftItem.item) return;

    // Clone the item to move
    const itemToMove = { ...selectedLeftItem.item };

    // Remove from left list
    const newLeftItems = removeItemByPath(leftItems, selectedLeftItem.path);

    // Add to right list (either as root or as child of selected right item)
    let newRightItems;
    if (selectedRightItem.item) {
      newRightItems = addItemToParent(rightItems, selectedRightItem.path, itemToMove);
    } else {
      newRightItems = [...rightItems, itemToMove];
    }

    // Update state
    setLeftItems(newLeftItems);
    setRightItems(newRightItems);
    setSelectedLeftItem({item: null, path: []});
  };

  // Move selected item from right to left
  const moveSelectedToLeft = () => {
    if (!selectedRightItem.item) return;

    // Clone the item to move
    const itemToMove = { ...selectedRightItem.item };

    // Remove from right list
    const newRightItems = removeItemByPath(rightItems, selectedRightItem.path);

    // Add to left list (either as root or as child of selected left item)
    let newLeftItems;
    if (selectedLeftItem.item) {
      newLeftItems = addItemToParent(leftItems, selectedLeftItem.path, itemToMove);
    } else {
      newLeftItems = [...leftItems, itemToMove];
    }

    // Update state
    setRightItems(newRightItems);
    setLeftItems(newLeftItems);
    setSelectedRightItem({item: null, path: []});
  };

  // Move all items from left to right
  const moveAllToRight = () => {
    // If a right item is selected, add all left items as its children
    if (selectedRightItem.item) {
      let newRightItems = [...rightItems];

      // Add each left item as a child of the selected right item
      leftItems.forEach(item => {
        newRightItems = addItemToParent(newRightItems, selectedRightItem.path, { ...item });
      });

      setRightItems(newRightItems);
    } else {
      // Otherwise, just append all left items to the right list
      setRightItems([...rightItems, ...leftItems.map(item => ({ ...item }))]);
    }

    // Clear left list
    setLeftItems([]);
    setSelectedLeftItem({item: null, path: []});
  };

  // Move all items from right to left
  const moveAllToLeft = () => {
    // If a left item is selected, add all right items as its children
    if (selectedLeftItem.item) {
      let newLeftItems = [...leftItems];

      // Add each right item as a child of the selected left item
      rightItems.forEach(item => {
        newLeftItems = addItemToParent(newLeftItems, selectedLeftItem.path, { ...item });
      });

      setLeftItems(newLeftItems);
    } else {
      // Otherwise, just append all right items to the left list
      setLeftItems([...leftItems, ...rightItems.map(item => ({ ...item }))]);
    }

    // Clear right list
    setRightItems([]);
    setSelectedRightItem({item: null, path: []});
  };

  // Recursive function to render tree items
  const renderTreeItems = (
    items: TreeItem[],
    onSelect: (item: TreeItem, path: string[]) => void,
    selectedItem: {item: TreeItem | null, path: string[]},
    currentPath: string[] = []
  ) => {
    return (
      <ul className="transfer-list-items">
        {items.map((item) => {
          const itemPath = [...currentPath, item.id];
          const isSelected = selectedItem.item && selectedItem.path.join('-') === itemPath.join('-');

          return (
            <li
              key={item.id}
              className={`transfer-list-item ${isSelected ? 'selected' : ''}`}
              onClick={(e) => {
                e.stopPropagation();
                onSelect(item, itemPath);
              }}
            >
              <div className="item-content">
                <span>{item.name}</span>
              </div>

              {item.children && item.children.length > 0 && (
                renderTreeItems(item.children, onSelect, selectedItem, itemPath)
              )}
            </li>
          );
        })}
      </ul>
    );
  };

  return (
    <div className="container">
      <div className="transfer-list-container">
        {/* Left List */}
        <div className="transfer-list-box">
          <h3 className="transfer-list-title">{leftTitle}</h3>
          <div className="transfer-list-content">
            {leftItems.length > 0 ? (
              renderTreeItems(
                leftItems,
                (item, path) => setSelectedLeftItem({item, path}),
                selectedLeftItem
              )
            ) : (
              <div className="empty-list">No hay elementos</div>
            )}
          </div>
        </div>

        {/* Transfer Buttons */}
        <div className="transfer-buttons">
          <button
            className="transfer-button"
            onClick={moveSelectedToRight}
            disabled={!selectedLeftItem.item}
            title="Mover elemento seleccionado a la derecha"
          >
            <span>&#8250;</span>
          </button>

          <button
            className="transfer-button"
            onClick={moveAllToRight}
            disabled={leftItems.length === 0}
            title="Mover todos los elementos a la derecha"
          >
            <span>&#187;</span>
          </button>

          <button
            className="transfer-button"
            onClick={moveSelectedToLeft}
            disabled={!selectedRightItem.item}
            title="Mover elemento seleccionado a la izquierda"
          >
            <span>&#8249;</span>
          </button>

          <button
            className="transfer-button"
            onClick={moveAllToLeft}
            disabled={rightItems.length === 0}
            title="Mover todos los elementos a la izquierda"
          >
            <span>&#171;</span>
          </button>
        </div>

        {/* Right List */}
        <div className="transfer-list-box">
          <h3 className="transfer-list-title">{rightTitle}</h3>
          <div className="transfer-list-content">
            {rightItems.length > 0 ? (
              renderTreeItems(
                rightItems,
                (item, path) => setSelectedRightItem({item, path}),
                selectedRightItem
              )
            ) : (
              <div className="empty-list">
                {(moduleFilter || roleKeyFilter) ?
                  `No hay elementos para los filtros seleccionados` :
                  "No hay elementos"
                }
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransferList;
