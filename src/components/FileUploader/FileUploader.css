.file-uploader {
  color: white !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
  justify-content: center;

  border: 1px solid #b3d4d4fc;
  border-radius: 18px;
  background: #f9f9f9;
  height: 40px;
  cursor: pointer;
  transition:
    all 0,
    2s ease-in-out;
  text-align: center;
  width: auto;
}
.f .file-label-row {
  display: flex;
  align-items: center;
  gap: 0.5 rem;
}
.file-uploader:hover {
  background-color: #eef6ff;
  box-shadow: 0 0 5px rgb(0, 111, 255, 0.3);
}

.file-icon {
  display: flex;

  gap: 0.15rem;
  cursor: pointer;

  border-radius: 6px;
  vertical-align: middle;
  margin-right: 8px;
}
.file-name {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: #333;
}
.file-error {
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: red;
}
.file-upload-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f5f9ff;
  border: 1px solid #d0e4ff;
  padding: 0.6rem 1rem;
  border-radius: 8px;
  font-size: 0.87rem;
  font-weight: 500;
  text-align: center;
  min-width: 180px;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #333;
}
.file-upload-button:hover {
  background-color: #e7f1ff;
  box-shadow: 0 0 0 1px #cce0ff;
}
