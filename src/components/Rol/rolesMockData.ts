import { TableInputType } from "../../enums";
import { TableColumn, TableData } from "../MetlifeComponents/Table/table-types";

const mockColumns: TableColumn[] = [
  {
    id: "table-head-1",
    label: "CLAVE",
    accessor: "id",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-2",
    label: "NOMBRE",
    accessor: "TROL_NOMBRE",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-3",
    label: "ESTADO",
    accessor: "TROL_IND_ACTIVO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-4",
    label: "FECHA ESTADO",
    accessor: "TROL_FEC_ACTIVO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.DATE,
  },
  {
    id: "table-head-5",
    label: "MODULO",
    accessor: "TROL_COD_MODULO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-6",
    label: "USUARIO CREO",
    accessor: "TROL_USER_CREO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-7",
    label: "FECHA CREO",
    accessor: "TROL_FEC_CREO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.DATE,
  },
  {
    id: "table-head-8",
    label: "USUARIO MOD",
    accessor: "TROL_USER_MOD",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-9",
    label: "FECHA MOD",
    accessor: "TROL_FEC_MOD",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.DATE,
  },
];

const mockData: TableData[] = [
  {
    id: "CASHADM",
    TROL_NOMBRE: "Administrador",
    TROL_IND_ACTIVO: "Activo",
    TROL_FEC_ACTIVO: "2017-05-15",
    TROL_COD_MODULO: "Ingresos",
    TROL_USER_CREO: "user",
    TROL_FEC_CREO: "2025-03-27",
    TROL_USER_MOD: null,
    TROL_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "CASHAPP",
    TROL_NOMBRE: "Captura de Pagos",
    TROL_IND_ACTIVO: "Activo",
    TROL_FEC_ACTIVO: "2016-04-10",
    TROL_COD_MODULO: "Ingresos",
    TROL_USER_CREO: "user",
    TROL_FEC_CREO: "2025-03-27",
    TROL_USER_MOD: null,
    TROL_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "CASHBANC",
    TROL_NOMBRE: "Banca Electronica",
    TROL_IND_ACTIVO: "Activo",
    TROL_FEC_ACTIVO: "2015-09-26",
    TROL_COD_MODULO: "Ingresos",
    TROL_USER_CREO: "user",
    TROL_FEC_CREO: "2025-03-27",
    TROL_USER_MOD: null,
    TROL_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "CASHPARA",
    TROL_NOMBRE: "Parametrizacion",
    TROL_IND_ACTIVO: "Activo",
    TROL_FEC_ACTIVO: "2014-06-02",
    TROL_COD_MODULO: "Ingresos",
    TROL_USER_CREO: "user",
    TROL_FEC_CREO: "2025-03-27",
    TROL_USER_MOD: null,
    TROL_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
];

export { mockData, mockColumns };
