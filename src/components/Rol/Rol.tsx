import { FC, useEffect, useState } from "react";
import {
  initialPostApi,
  useGet,
  usePost,
  UsePostProps,
} from "../../hooks/useFetch";
import Table from "../MetlifeComponents/Table/Table";
import {
  TableData,
  TableResponse,
} from "../MetlifeComponents/Table/table-types";
import MLDCReactLoadingIndicator from "@metlife/mldc-react-loading-indicator";
import { mockColumns, mockData } from "./rolesMockData";
import "./Rol.css";

type RolProps = {};

const Rol: FC<RolProps> = () => {
  const [callPostAPI, setCallPostAPI] = useState<UsePostProps>(initialPostApi);

  const { result: responsePost } = usePost(callPostAPI);
  const { result, loading, error, refetch } = useGet<TableResponse>({
    url: "https://api.example.com/",
    call: false, //true TODO: until have the BE
  });
  console.log({ result, loading, error });

  const onSaveRol = (newElement: TableData) => {
    setCallPostAPI({
      url: "https://api.example.com/",
      call: true,
      data: newElement,
    });
  };

  const onEditRol = (editElement: TableData) => {
    setCallPostAPI({
      url: `https://api.example.com/${editElement?.id}`,
      call: true,
      method: "PUT",
      data: editElement,
    });
  };

  const onDeleteRol = (elementId: number) => {
    setCallPostAPI({
      url: `https://api.example.com/${elementId}`,
      call: true,
      method: "DELETE",
    });
  };

  useEffect(() => {
    if (responsePost != null) {
      refetch();
      setCallPostAPI(initialPostApi);
    }
  }, [responsePost, refetch]);

  if (loading)
    return (
      <MLDCReactLoadingIndicator id="Loading" className="loading-wrapper" />
    );
  if (error) return <>{`Error: ${error.message}`}</>;

  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE ROLES</h1>
      <Table
        id="Roles"
        // data={result?.data ?? []}
        // columns={result?.columns ?? []}
        data={mockData}
        columns={mockColumns}
        filters={["id", "TROL_NOMBRE"]}
        onSave={onSaveRol}
        onEdit={onEditRol}
        onDelete={onDeleteRol}
      />
    </div>
  );
};

export default Rol;
