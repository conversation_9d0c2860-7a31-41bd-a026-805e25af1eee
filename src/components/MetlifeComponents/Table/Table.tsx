import { FC, useState } from "react";
import MLDCReactDataTable from "@metlife/mldc-react-data-table";
import TableSlidePanel from "./TableSlidePanel";
import TableFilters from "./TableFilters";
import { TableColumn, TableData } from "./table-types";
import "./table.css";

type TableProps = {
  id: string;
  data: TableData[];
  filters?: string[];
  columns: TableColumn[];
  onSave?: (element: TableData) => void;
  onEdit?: (element: TableData) => void;
  onDelete?: (elementId: number) => void;
};

const Table: FC<TableProps> = ({
  id,
  data,
  filters,
  columns,
  onSave,
  onEdit,
  onDelete,
}) => {
  const [editedData, setEditedData] = useState<TableData>();
  const [openPanel, setOpenPanel] = useState<boolean>(false);
  const [isEditingRow, setIsEditingRow] = useState<boolean>(false);
  const [filteredData, setFilteredData] = useState<TableData[]>(data);

  const showingColumns = columns.filter((col) => !col.hidden);
  const editableColumns = columns.filter((col) => col.editable || isEditingRow);

  const _onAddAction = () => {
    const newElement = columns.reduce((acc, col) => {
      const ele = document.getElementById(
        `${col.id}-input`
      ) as HTMLInputElement;
      return {
        ...acc,
        [col.accessor]: col.editable ? ele?.value : "",
      };
    }, {});
    onSave?.(newElement);
    setOpenPanel(false);
  };

  const _onEditAction = () => {
    const editedElement = editableColumns.reduce((acc, col) => {
      const ele = document.getElementById(
        `${col.id}-input`
      ) as HTMLInputElement;
      return {
        ...acc,
        [col.accessor]: ele?.value,
      };
    }, {});
    onEdit?.(editedElement);
    setOpenPanel(false);
  };

  const _onDeleteAction = (dataId: number) => {
    onDelete?.(dataId);
  };

  const _onEditActionButton = (dataId: number) => {
    setEditedData(filteredData.find((d) => d.id === dataId));
    setOpenPanel(true);
  };

  const _onSaveClick = (isEditing: boolean) => {
    if (!isEditing) _onAddAction();
    else _onEditAction();
  };

  return (
    <>
      {filters && (
        <TableFilters
          data={data}
          columns={columns}
          filters={filters}
          onFilterChange={setFilteredData}
        />
      )}
      <MLDCReactDataTable
        id={`${id}-data-table`}
        data={filteredData}
        columns={showingColumns}
        actionIcons
        enableAddRow
        pagination={true}
        disableStackedView={true}
        onAddRow={() => {
          setIsEditingRow(false);
          setOpenPanel(true);
        }}
        onEditRow={(_, dataId: number) => {
          setIsEditingRow(true);
          _onEditActionButton(dataId);
        }}
        onDeleteRow={(_, dataId: number) => {
          _onDeleteAction(dataId);
        }}
        style={{ maxHeight: "70vh", minHeight: "35vh" }}
      />
      <TableSlidePanel
        data={editedData}
        columns={editableColumns}
        openPanel={openPanel}
        isEditingRow={isEditingRow}
        setOpenPanel={setOpenPanel}
        onSave={() => _onSaveClick(isEditingRow)}
      />
    </>
  );
};
export default Table;
