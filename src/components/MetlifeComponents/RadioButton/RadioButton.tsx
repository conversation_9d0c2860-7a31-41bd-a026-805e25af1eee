import MLDCReactRadio from "@metlife/mldc-react-radio";
import { RadioOption } from "@metlife/mldc-react-radio/lib/component";
import "./RadioButton.css";
type Props = {
  options: RadioOption[];
  d: string;
  id: string;
  style: any;
};
const RadioButton = ({ options, d, id, style }: Props) => {
  return (
    <div className="radio-container">
      <MLDCReactRadio
        defaultCheckedValue=""
        groupLabel=""
        id={id}
        onChange={function noRefCheck() {}}
        options={options}
      />
      <div data-testid="placeholder" id="placeholder" style={style} />
    </div>
  );
};
export default RadioButton;
