import MLDCReactCheckbox from "@metlife/mldc-react-checkbox";
type Props = {
  value: string;
  label: string;
};
const CheckBox = ({ value, label }: Props) => {
  return (
    <div>
      <MLDCReactCheckbox
        id="items"
        onChange={function noRefCheck() {}}
        // validationMessage="Error message goes here"
        value={value}
      >
        <span>{label}</span>
      </MLDCReactCheckbox>
      <div
        data-testid="placeholder"
        id="placeholder"
        style={{
          marginTop: "20px",
        }}
      />
    </div>
  );
};

export default CheckBox;
