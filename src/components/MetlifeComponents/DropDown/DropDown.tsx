import MLDCReactDropdown from "@metlife/mldc-react-dropdown";
import { DropDownOption } from "./DropDownOption";
import "./DropDown.css";
type Props = {
  change: any;
  opts: DropDownOption[];
  click: any;
  style: any;
  placeholder: string;
  disabled: boolean;
  value?: any;
  styleDrop?: any;
  selectedValue?: string;
};
const DropDown = ({
  change,
  opts,
  click,
  style,
  placeholder,
  disabled = false,
  value,
  selectedValue,
  styleDrop,
}: Props) => {
  const useIds = new Set();
  function generateUniqueId(min = 1, max = 30000) {
    let id;
    do {
      id = Math.floor(Math.random() * (max - min + 1)) + min;
    } while (useIds.has(id));
    useIds.add(id);
    return id;
  }

  const uniqueId = generateUniqueId().toString();
  return (
    <div style={style}>
      <MLDCReactDropdown
        style={styleDrop}
        className="dropdown"
        placeholder={placeholder}
        id={uniqueId}
        disabled={disabled}
        listBoxStyle={{
          zIndex: "4",
          whiteSpace: "nowrap",
        }}
        onChange={change}
        onClick={click}
        options={opts}
        label={""}
      />
      <div />
    </div>
  );
};

export default DropDown;
