.sidebar {
  height: 100vh;
  justify-items: center;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 3px 3px 10px 3px #6565654f;

  .sticky-collapse-button {
    top: 10px;
    width: 25px;
    height: 30px;
    padding-left: 5px;
    border-radius: 5px;
    position: absolute;
    align-content: center;
    background-color: #fff;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .image-wrap {
    padding: 20px;
    width: 240px;
  }

  .left-nav {
    padding-top: 25px;
    nav {
      .use-nav-link {
        min-width: 300px;
        max-width: 300px;
        .nav-item {
          font-size: var(--px-size-12);
        }
      }
    }
  }

  .collapsed-options {
    text-align: center;

    .icon-option-active {
      border: 6px solid #a4ce4e !important;
    }
  }
}

.sidebar.expanded {
  width: 300px;

  .sticky-collapse-button {
    left: 300px;
  }
}
.sidebar.collapsed {
  width: 80px;

  /* .image-wrap {
    margin-left: 157px;
  } */

  .sticky-collapse-button {
    left: 80px;
  }
}

.menu-banner {
  background-color: #0061a0 !important;
  color: #ffff !important;
  font-size: var(--px-size-12) !important;
}

.level3 {
  padding-left: 15px !important;
  white-space: nowrap;
  text-transform: uppercase;
  cursor: pointer;
}
