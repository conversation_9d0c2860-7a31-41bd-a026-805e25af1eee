import { FC, useState } from "react";
import { FolderPlus } from "react-bootstrap-icons";
import { FolderMinus } from "react-bootstrap-icons";
import MLDCReactIcon from "@metlife/mldc-icons";
import MLDCReactLeftNav from "@metlife/mldc-react-left-nav";
import MetlifeLogo from "../../../assets/MetLife.svg";
import "./sidebar.css";
import { useNavigate } from "react-router-dom";

const Sidebar: FC<any> = () => {
  const [activeNav, setActiveNav] = useState<string>("/home");
  const [isCollapseNavBar, setIsCollapseNavBar] = useState<boolean>(false);
  const navigate = useNavigate();

  const _onClick = (path: string) => {
    setActiveNav(path);
  };
  //Handle  Catalogos Menu Option  childs  visibility
  const [CatalogoChildVisible, setCatalogoChildVisible] = useState(true);
  // Handle Route Clicks
  const handleMantenimientoCompaniasClick = () => {
    navigate("mantenimiento-companias");
  };
  const handleMantenimientoBancosClick = () => {
    navigate("mantenimiento-bancos");
  };
  const HandleMantenimientoCuentasClick = () => {
    navigate("mantenimiento-cuenta");
  };
  const HandleMantenimientoUsuariosClick = () => {
    navigate("mantenimiento-usuario");
  };
  const handleMantenimientoConceptoClick = () => {
    navigate("mantenimiento-concepto");
  };
  const handleMantenimientoPlantillaClick = () => {
    navigate("mantenimiento-plantilla");
  };
  const handleMantenimientoPlantillasContablesClick = () => {
    navigate("mantenimiento-plantillas-contables");
  };
  const handleMantenimientoEstadoCuentaClick = () => {
    navigate("mantenimiento-estado");
  };
  const handleContaIngresoClick = () => {
    navigate("contabilizacion");
  };
  const handleRolClick = () => {
    navigate("rol");
  };
  const handleMenuClick = () => {
    navigate("menu");
  };
  const handleMenuPorRolClick = () => {
    navigate("menu-rol");
  };

  const handleCatalogosClick = () => {
    setCatalogoChildVisible(!CatalogoChildVisible);
  };
  const [ConsultasChildVisible, setConsultasChildVisible] = useState(true);
  const handleConsultasClick = () => {
    setConsultasChildVisible(!ConsultasChildVisible);
  };
  const [ProcesosChildVisible, setProcesosChildVisible] = useState(true);
  const handleProcesosClick = () => {
    setProcesosChildVisible(!ProcesosChildVisible);
  };
  const [IngresosChildVisible, setIngresosChildVisible] = useState(true);
  const handleIngresosChildVisibleClick = () => {
    setIngresosChildVisible(!IngresosChildVisible);
  };
  const [ReportesChildVisible, setReportesChildVisible] = useState(true);
  /* const handleReportesChildVisibleClick = () => {
    setReportesChildVisible(!ReportesChildVisible);
  }; */
  const [CobranzaChildVisible, setCobranzaChildVisible] = useState(true);
  const handleCobranzaChildVisibleClick = () => {
    setCobranzaChildVisible(!CobranzaChildVisible);
  };
  const [MantenimientoChildVisible, setMantenimientoChildVisible] =
    useState(true);
  const handleMantenimientoChildVisibleClick = () => {
    setMantenimientoChildVisible(!MantenimientoChildVisible);
  };

  const nodes = [
    <div
      className="menu-banner"
      role="link"
      aria-label="Home"
      tabIndex={0}
      onClick={() => _onClick("/home")}
    >
      <div className="menu-banner">MENU PRINCIPAL INGRESOS</div>
    </div>,
    <div
      className={activeNav === "/catalogos" ? "active" : "inactive"}
      role="link"
      aria-label="Catalogos"
      tabIndex={0}
      onClick={handleCatalogosClick}
    >
      <div className="nav-icon">
        {CatalogoChildVisible && <FolderPlus />}
        {!CatalogoChildVisible && <FolderMinus />}
      </div>
      <div className="nav-item">CATALOGOS</div>
    </div>,
    !CatalogoChildVisible && (
      <div onClick={handleMantenimientoCompaniasClick}>
        <div className="level3" aria-label="Catalogos" tabIndex={0}>
          <div className="nav-item">Mantenimiento de Compañias</div>
        </div>
      </div>
    ),
    !CatalogoChildVisible && (
      <div onClick={handleMantenimientoBancosClick}>
        <div className="level3" aria-label="Catalogos" tabIndex={0}>
          <div className="nav-item">Mantenimiento de Bancos</div>
        </div>
      </div>
    ),
    !CatalogoChildVisible && (
      <div onClick={HandleMantenimientoCuentasClick}>
        <div className="level3" aria-label="Catalogos" tabIndex={0}>
          <div className="nav-item">Mantenimiento de Cuentas Bancarias</div>
        </div>
      </div>
    ),
    !CatalogoChildVisible && (
      <div onClick={handleMantenimientoConceptoClick}>
        <div className="level3" aria-label="Catalogos" tabIndex={0}>
          <div className="nav-item">Mantenimiento de Conceptos</div>
        </div>
      </div>
    ),

    !CatalogoChildVisible && (
      <div>
        <div
          className="level3"
          onClick={handleMantenimientoPlantillasContablesClick}
          aria-label="Catalogos"
          tabIndex={0}
        >
          <div className="nav-item">Mantenimiento de Plantillas Contables</div>
        </div>
      </div>
    ),

    !ConsultasChildVisible && (
      <div
        className={activeNav === "/ingresos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">CONSULTA DE MOVIMIENTOS HIST.</div>
      </div>
    ),
    !ConsultasChildVisible && (
      <div
        className={activeNav === "/ingresos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">MONITOREO DE CAJAS</div>
      </div>
    ),

    !IngresosChildVisible && (
      <div
        className={activeNav === "/ingresos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">CAPTURA DE INGRESOS PENDIENTES</div>
      </div>
    ),
    !IngresosChildVisible && (
      <div
        className={activeNav === "/ingresos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">
          REPORTE DE MOVIMIENTOS DE INGRESOS POR AREA/FECHA
        </div>
      </div>
    ),

    !ProcesosChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">DOTACIONES A CAJEROS</div>
      </div>
    ),
    !ProcesosChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">REGISTRO DE INGRESOS Y EGRESOS DIARIOS</div>
      </div>
    ),
    !ProcesosChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">CORTE DE CAJA NORMAL</div>
      </div>
    ),
    !ProcesosChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">CORTE DE CAJA GENERAL</div>
      </div>
    ),
    !ProcesosChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">ANULACION DE OPERACIONES</div>
      </div>
    ),
    !ProcesosChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">
          REPORTE DE MOVIMIENTOS DE INGRESOS POR CAJA Y FECHA
        </div>
      </div>
    ),
    !ProcesosChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">CONTABILIZACION DE MOVIMIENTOS DIARIOS</div>
      </div>
    ),
    !ProcesosChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">REPORTE DE PRODUCTIVIDAD</div>
      </div>
    ),
    !ProcesosChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">CORTE DE REMESA</div>
      </div>
    ),

    !ReportesChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">COMPROBANTES DE INGRESOS HISTORICOS</div>
      </div>
    ),
    !ReportesChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">
          REPORTES DE MOVIMIENTOS POR CAJA Y FECHA{" "}
        </div>
      </div>
    ),
    !ReportesChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">
          REPORTES DE MOVIMIENTOS POR CONCEPTO Y FECHA{" "}
        </div>
      </div>
    ),
    !ReportesChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">
          REPORTES DE MOVIMIENTOS POR IMPORTE Y FECHA{" "}
        </div>
      </div>
    ),
    !ReportesChildVisible && (
      <div
        className={activeNav === "/procesos" ? "active" : "inactive"}
        role="link"
        aria-label="Ingresos"
        tabIndex={0}
      >
        <div className="nav-item">
          REPORTES DE MOVIMIENTOS POR CAJERO Y FECHA{" "}
        </div>
      </div>
    ),
    <div
      className={activeNav === "/procesos" ? "active" : "inactive"}
      role="link"
      aria-label="Consultas"
      tabIndex={0}
      onClick={handleCobranzaChildVisibleClick}
    >
      <div className="nav-icon">
        {CobranzaChildVisible && <FolderPlus />}
        {!CobranzaChildVisible && <FolderMinus />}
      </div>
      <div className="nav-item">INGRESOS BANCOS</div>
    </div>,

    !CobranzaChildVisible && (
      <div>
        <div
          className="level3"
          onClick={handleMantenimientoEstadoCuentaClick}
          aria-label="Catalogos"
          tabIndex={0}
        >
          <div className="nav-item">Mantenimiento de Estado de Cuenta</div>
        </div>
      </div>
    ),

    !CobranzaChildVisible && (
      <div>
        <div
          className="level3"
          onClick={handleContaIngresoClick}
          aria-label="Catalogos"
          tabIndex={0}
        >
          <div className="nav-item">Contabilización de Ingresos</div>
        </div>
      </div>
    ),
    <div
      className={activeNav === "/procesos" ? "active" : "inactive"}
      role="link"
      aria-label="Consultas"
      tabIndex={0}
      onClick={handleMantenimientoChildVisibleClick}
    >
      <div className="nav-icon">
        {MantenimientoChildVisible && <FolderPlus />}
        {!MantenimientoChildVisible && <FolderMinus />}
      </div>
      <div className="nav-item">GESTIÓN DE ACCESOS</div>
    </div>,
    !MantenimientoChildVisible && (
      <div>
        <div
          className="level3"
          onClick={handleRolClick}
          aria-label="Catalogos"
          tabIndex={0}
        >
          <div className="nav-item">Mantenimiento de Roles</div>
        </div>
      </div>
    ),
    !MantenimientoChildVisible && (
      <div>
        <div
          className="level3"
          onClick={handleMenuClick}
          aria-label="Catalogos"
          tabIndex={0}
        >
          <div className="nav-item">Mantenimiento de Menú</div>
        </div>
      </div>
    ),
    !MantenimientoChildVisible && (
      <div>
        <div
          className="level3"
          onClick={handleMenuPorRolClick}
          aria-label="Catalogos"
          tabIndex={0}
        >
          <div className="nav-item">Menú por Rol</div>
        </div>
      </div>
    ),
    !MantenimientoChildVisible && (
      <div>
        <div
          className="level3"
          onClick={HandleMantenimientoUsuariosClick}
          aria-label="Catalogos"
          tabIndex={0}
        >
          <div className="nav-item">Mantenimiento de Usuarios</div>
        </div>
      </div>
    ),
  ];

  return (
    <div className={`sidebar ${isCollapseNavBar ? "collapsed" : "expanded"}`}>
      <div className="sticky-collapse-button">
        <MLDCReactIcon
          svgName="ChevronRight"
          style={{
            fontSize: "13px",
            cursor: "pointer",
            transform: `${isCollapseNavBar ? "rotate(0deg)" : "rotate(180deg)"}`,
            transition: "all 0.5s",
          }}
          onClick={() => setIsCollapseNavBar((prev) => !prev)}
        />
      </div>
      <img className="image-wrap" alt="logo" src={MetlifeLogo} />
      {isCollapseNavBar ? null : (
        <MLDCReactLeftNav id="left-nav" className="left-nav" navLink={nodes} />
      )}
    </div>
  );
};
export default Sidebar;
