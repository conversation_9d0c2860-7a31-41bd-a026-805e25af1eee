import { TableInputType } from "../../enums";
import { TableColumn, TableData } from "../MetlifeComponents/Table/table-types";

const mockColumns: TableColumn[] = [
  {
    id: "table-head-1",
    label: "ID USUARIO",
    accessor: "TCME_COD_MODULO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-2",
    label: "NOMBRE",
    accessor: "TCME_ID_NIVEL",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-3",
    label: "FECHA DESDE",
    accessor: "TCME_NODO_MAESTRO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-4",
    label: "<PERSON>EC<PERSON> HASTA",
    accessor: "TCME_NODO_DEPENDIENTE",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-5",
    label: "ROL",
    accessor: "TCME_DESCRIPCION",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-6",
    label: "NUMERO DE EMPLEADO",
    accessor: "TCME_COD_MENU",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.DATE,
  },
  {
    id: "table-head-7",
    label: "STATUS",
    accessor: "TCME_COG_SUB_MENU",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  /*{
    id: "table-head-8",
    label: "ESTADO",
    accessor: "TCME_NOM_COMPONENTE",
    isLink: false,
    sortable: true,
    type: TableInputType.DATE,
  },
  {
    id: "table-head-9",
    label: "NOMBRE OBJETO FISICO PANTALLA",
    accessor: "TCME_VER_COMPONENTE",
    isLink: false,
    sortable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-10",
    label: "FECHA MOD",
    accessor: "TCIA_FEC_MOD",
    isLink: false,
    sortable: true,
    type: TableInputType.DATE,
  },*/
];

const mockData: TableData[] = [
  {
    TCME_COD_MODULO: "e1976432",
    TCME_ID_NIVEL: "Usuario01",
    TCME_NODO_MAESTRO: "14/01/2016",
    TCME_NODO_DEPENDIENTE: "05/04/2016",
    TCME_DESCRIPCION: "CASHCONF",
    TCME_ACTIVO: "1976432",
    TCME_COD_MENU: "ACTIVO",
    TCME_COG_SUB_MENU: null,
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "1",
  },
  {
    TCME_COD_MODULO: "e1976433",
    TCME_ID_NIVEL: "Usuario02",
    TCME_NODO_MAESTRO: "14/09/2018",
    TCME_NODO_DEPENDIENTE: "05/04/2018",
    TCME_DESCRIPCION: "CASHAPP",
    TCME_ACTIVO: "1976432",
    TCME_COD_MENU: "ACTIVO",
    TCME_COG_SUB_MENU: null,
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "2",
  },
  {
    TCME_COD_MODULO: "e1976434",
    TCME_ID_NIVEL: "Usuario03",
    TCME_NODO_MAESTRO: "19/04/2017",
    TCME_NODO_DEPENDIENTE: "05/04/2017",
    TCME_DESCRIPCION: "CASHADM",
    TCME_ACTIVO: "1976432",
    TCME_COD_MENU: "ACTIVO",
    TCME_COG_SUB_MENU: null,
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "3",
  },
  /*{
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "2",
    TCME_NODO_MAESTRO: "300",
    TCME_NODO_DEPENDIENTE: "0",
    TCME_DESCRIPCION: "Ingresos",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Ingresos",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "4",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "2",
    TCME_NODO_MAESTRO: "400",
    TCME_NODO_DEPENDIENTE: "0",
    TCME_DESCRIPCION: "Procesos",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Procesos",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "5",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "2",
    TCME_NODO_MAESTRO: "500",
    TCME_NODO_DEPENDIENTE: "0",
    TCME_DESCRIPCION: "Reportes",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Reportes",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "6",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "110",
    TCME_NODO_DEPENDIENTE: "100",
    TCME_DESCRIPCION: "Claves",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Claves",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "7",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "120",
    TCME_NODO_DEPENDIENTE: "100",
    TCME_DESCRIPCION: "Plantilla contables",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Plantilla contables",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "8",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "130",
    TCME_NODO_DEPENDIENTE: "100",
    TCME_DESCRIPCION: "Tipos de cambio",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Tipos de cambio",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "9",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "210",
    TCME_NODO_DEPENDIENTE: "200",
    TCME_DESCRIPCION: "Monitoreo de Cajas",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Monitoreo_Cajas",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "10",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "220",
    TCME_NODO_DEPENDIENTE: "200",
    TCME_DESCRIPCION: "Consulta de Movimientos Históricos",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Consul_Mov_Hist",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "11",
  },*/
];

export { mockData, mockColumns };
