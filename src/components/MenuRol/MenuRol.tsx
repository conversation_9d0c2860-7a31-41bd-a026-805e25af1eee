import { FC, useState } from "react";
import TransferList from "../TransferList/TransferList";
import DropDown from "../MetlifeComponents/DropDown/DropDown";
import { DropDownOption } from "../MetlifeComponents/DropDown/DropDownOption";

const MenuRol: FC = () => {
  // State for filters
  const [selectedRoleKey, setSelectedRoleKey] = useState<string>("");
  const [selectedModule, setSelectedModule] = useState<string>("");

  // Role options for the dropdown
  const roleKeyOptions: DropDownOption[] = [
    {
      ariaLabel: "Administrador",
      label: "Administrador",
      value: "admin",
    },
    {
      ariaLabel: "Cajero",
      label: "Cajero",
      value: "cajero",
    },
    {
      ariaLabel: "Supervisor",
      label: "Supervisor",
      value: "supervisor",
    },
    {
      ariaLabel: "Contador",
      label: "Contador",
      value: "contador",
    },
    {
      ariaLabel: "Auditor",
      label: "Auditor",
      value: "auditor",
    },
    {
      ariaLabel: "Gerente",
      label: "Gerente",
      value: "gerente",
    },
  ];

  const handleRoleKeyChange = (value: any) => {
    // Handle both string and object cases
    const selectedValue = typeof value === 'string' ? value : value?.value || value?.target?.value || '';
    setSelectedRoleKey(selectedValue);
  };

  return (
    <div className="container">
      <h2 className="title">Menú Por Rol</h2>

      {/* Filter Controls */}
      <div style={{ marginBottom: "20px", display: "flex", gap: "20px", alignItems: "center" }}>
        <div style={{ display: "flex", flexDirection: "column", gap: "5px" }}>
          <label htmlFor="role-key-select" style={{ fontWeight: "bold" }}>Clave Rol:</label>
          <DropDown
            opts={roleKeyOptions}
            change={handleRoleKeyChange}
            click={() => {}}
            placeholder="Seleccione Clave Rol"
            selectedValue={selectedRoleKey}
            disabled={false}
            style={{ width: "200px" }}
          />
        </div>
      </div>

      <TransferList
        leftTitle="Menús Disponibles"
        rightTitle="Menús Asignados"
        roleKeyFilter={selectedRoleKey}
      />
    </div>
  );
};

export default MenuRol;