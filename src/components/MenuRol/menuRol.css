/* Estilos para el componente MenuRol */

/* Alineación vertical del input de descripción */
.descripcion-filter-input {
  height: 50px;
}

/* Asegurar que el contenedor del input tenga la altura correcta */
.descripcion-filter-input .mldc-input-text-container {
  height: 50px;
  display: flex;
  align-items: center;
}

/* Alineación del grupo del input */
.descripcion-filter-input .mldc-input-text-group {
  height: 50px;
  display: flex;
  align-items: center;
}

/* Alineación del input group */
.descripcion-filter-input .input-group {
  height: 50px;
  display: flex;
  align-items: center;
}

/* Estilo del input field para centrar el placeholder */
.descripcion-filter-input input {
  height: 50px;
  line-height: 1.5;
  padding: 12px 16px;
  box-sizing: border-box;
  vertical-align: middle;
}

/* Centrar el placeholder verticalmente */
.descripcion-filter-input input::placeholder {
  line-height: 1.5;
  vertical-align: middle;
  opacity: 0.7;
}

/* Asegurar que el label no interfiera */
.descripcion-filter-input label {
  line-height: normal;
}
