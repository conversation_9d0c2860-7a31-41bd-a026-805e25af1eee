import { TableInputType } from "../../enums";
import { TableColumn, TableData } from "../MetlifeComponents/Table/table-types";

const mockColumns: TableColumn[] = [
  {
    id: "table-head-1",
    label: "ID",
    accessor: "id",
    isLink: false,
    sortable: true,
    hidden: true,
    editable: false,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-2",
    label: "MODULO",
    accessor: "TCME_COD_MODULO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-3",
    label: "NIVEL",
    accessor: "TCME_ID_NIVEL",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-4",
    label: "CLAVE DE NODO MAESTRO",
    accessor: "TCME_NODO_MAESTRO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-5",
    label: "CLAVE DE NODO DEPENDIENTE",
    accessor: "TCME_NODO_DEPENDIENTE",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-6",
    label: "DESCRIPCION DE LA OPCION",
    accessor: "TCME_DESCRIPCION",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-7",
    label: "NOMBRE OBJETO FISICO",
    accessor: "TCME_COD_MENU",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-8",
    label: "NOMBRE OBJETO FISICO DEPENDIENTE",
    accessor: "TCME_COG_SUB_MENU",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-9",
    label: "ESTADO",
    accessor: "TCME_NOM_COMPONENTE",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-10",
    label: "NOMBRE OBJETO FISICO PANTALLA",
    accessor: "TCME_VER_COMPONENTE",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },

  {
    id: "table-head-11",
    label: "USUARIO CREO",
    accessor: "TCME_USER_CREO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-12",
    label: "FECHA CREO",
    accessor: "TCME_FEC_CREO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.DATE,
  },
  /*{
    id: "table-head-13",
    label: "USUARIO MOD",
    accessor: "TCME_USER_MOD",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.TEXT,
  },
  /*{
    id: "table-head-14",
    label: "FECHA MOD",
    accessor: "TCME_FEC_MOD",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.DATE,
  },*/
];

const mockData: TableData[] = [
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "1",
    TCME_NODO_MAESTRO: "0",
    TCME_NODO_DEPENDIENTE: "0",
    TCME_DESCRIPCION: "MENU PRINCIPAL INGRESOS",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: null,
    TCME_COG_SUB_MENU: null,
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    TCME_USER_CREO: "USER",
    TCME_FEC_CREO: "01/04/2025",
    TCME_USER_MOD: null,
    TCME_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "1",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "2",
    TCME_NODO_MAESTRO: "100",
    TCME_NODO_DEPENDIENTE: "0",
    TCME_DESCRIPCION: "Catálogos",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Catalogos",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    TCME_USER_CREO: "USER",
    TCME_FEC_CREO: "01/04/2025",
    TCME_USER_MOD: null,
    TCME_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "2",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "2",
    TCME_NODO_MAESTRO: "200",
    TCME_NODO_DEPENDIENTE: "0",
    TCME_DESCRIPCION: "Consultas",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Consultas",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    TCME_USER_CREO: "USER",
    TCME_FEC_CREO: "01/04/2025",
    TCME_USER_MOD: null,
    TCME_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "3",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "2",
    TCME_NODO_MAESTRO: "300",
    TCME_NODO_DEPENDIENTE: "0",
    TCME_DESCRIPCION: "Ingresos",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Ingresos",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    TCME_USER_CREO: "USER",
    TCME_FEC_CREO: "01/04/2025",
    TCME_USER_MOD: null,
    TCME_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "4",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "2",
    TCME_NODO_MAESTRO: "400",
    TCME_NODO_DEPENDIENTE: "0",
    TCME_DESCRIPCION: "Procesos",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Procesos",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    TCME_USER_CREO: "USER",
    TCME_FEC_CREO: "01/04/2025",
    TCME_USER_MOD: null,
    TCME_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "5",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "2",
    TCME_NODO_MAESTRO: "500",
    TCME_NODO_DEPENDIENTE: "0",
    TCME_DESCRIPCION: "Reportes",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Reportes",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    TCME_USER_CREO: "USER",
    TCME_FEC_CREO: "01/04/2025",
    TCME_USER_MOD: null,
    TCME_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "6",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "110",
    TCME_NODO_DEPENDIENTE: "100",
    TCME_DESCRIPCION: "Claves",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Claves",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    TCME_USER_CREO: "USER",
    TCME_FEC_CREO: "01/04/2025",
    TCME_USER_MOD: null,
    TCME_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "7",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "120",
    TCME_NODO_DEPENDIENTE: "100",
    TCME_DESCRIPCION: "Plantilla contables",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Plantilla contables",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    TCME_USER_CREO: "USER",
    TCME_FEC_CREO: "01/04/2025",
    TCME_USER_MOD: null,
    TCME_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "8",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "130",
    TCME_NODO_DEPENDIENTE: "100",
    TCME_DESCRIPCION: "Tipos de cambio",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Tipos de cambio",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    TCME_USER_CREO: "USER",
    TCME_FEC_CREO: "01/04/2025",
    TCME_USER_MOD: null,
    TCME_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "9",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "210",
    TCME_NODO_DEPENDIENTE: "200",
    TCME_DESCRIPCION: "Monitoreo de Cajas",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Monitoreo_Cajas",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    TCME_USER_CREO: "USER",
    TCME_FEC_CREO: "01/04/2025",
    TCME_USER_MOD: null,
    TCME_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "10",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "220",
    TCME_NODO_DEPENDIENTE: "200",
    TCME_DESCRIPCION: "Consulta de Movimientos Históricos",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Consul_Mov_Hist",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    TCME_USER_CREO: "USER",
    TCME_FEC_CREO: "01/04/2025",
    TCME_USER_MOD: null,
    TCME_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "11",
  },
];

export { mockData, mockColumns };
