import { FC } from "react";
import Table from "../MetlifeComponents/Table/Table";
import { mockColumns, mockData } from "./menuMockData";

type CompaniaProps = {};

const Compania: FC<CompaniaProps> = () => {
  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE MENU</h1>
      <Table
        id="menu"
        data={mockData}
        columns={mockColumns}
        filters={["TCME_COD_MODULO", "TCME_ID_NIVEL", "TCME_NODO_MAESTRO"]}
      />
    </div>
  );
};

export default Compania;
