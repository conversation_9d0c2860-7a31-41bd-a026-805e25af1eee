import { FC, useState } from "react";
import "./Estado.css";
import DropDown from "../MetlifeComponents/DropDown/DropDown";
import Button from "../MetlifeComponents/Button/Button";
import FileUploader from "../FileUploader/FileUploader";
import CheckBox from "../MetlifeComponents/SingleCheckBox/SingleCheckBox";
import RadioButton from "../MetlifeComponents/RadioButton/RadioButton";
import { RadioButtonOption } from "../MetlifeComponents/RadioButton/RadioButtonOption";
import { click } from "@testing-library/user-event/dist/click";
import Modal from "./Modal/Modal";
import DatePicker from "../MetlifeComponents/DatePicker/DatePicker";
import { useNavigate } from "react-router-dom";
import { TextWrap } from "react-bootstrap-icons";
import EstadoButtons from "./EstadoButtons/EstadoButtons";
import EstadoDropDowns from "./EstadoDropDowns/EstadoDropDowns";
type EstadoProps = {};

const Estado = () => {
  const navigate = useNavigate();
  const [BancoSeleccionado, setBancoSeleccionado] = useState("");
  const FileOnUpload = () => {};
  const DropDownBancosHandleClick = () => {};
  const DropDownCuentasHandleClick = () => {};
  const DropDownBancosHandleOnChange = (value: string) => {
    setBancoSeleccionado(value);
  };
  const DropDownCuentasHandleOnchange = () => {};
  const handleUpload = (file: File) => {};
  const DropDownBancosStyle = {
    width: "225.29px",
    height: "50px",
    marginTop: "1rem",
  };
  const DropDownCuentasStyle = {
    width: "225.29x",
    height: "50px",
    marginTop: "0.5rem",
  };

  const DropDownBancosOptions = [
    {
      ariaLabel: "BBVA",
      label: "BBVA",
      value: "BBVA",
    },
    {
      ariaLabel: "City",
      label: "City",
      value: "City",
    },
    {
      ariaLabel: "HSBC",
      label: "HSBC",
      value: "HSBC",
    },
    {
      ariaLabel: "JPMorgan",
      label: "JPMorgan",
      value: "JPMorgan",
    },
  ];
  const optionsR = [
    {
      label: "Formato Tradicional",
      value: "tradicional",
    },
    {
      label: "MT490",
      value: "MT490",
    },
  ];
  const radioStyle = { display: "flex", TextWrap: "nowrap" };
  const DropDownCuentasOptions = [
    {
      ariaLabel: "1234",
      label: "1234",
      value: "1234",
    },
    {
      ariaLabel: "2323232323",
      label: "2323232323",
      value: "2323232323",
    },
    {
      ariaLabel: "4545465464",
      label: "545465464",
      value: "545465464",
    },
  ];

  const [showModal, setShowModal] = useState(false);
  const [fechaInicial, setFechaInicial] = useState("");
  const [fechaFinal, setFechaFinal] = useState("");
  return (
    <div className="estado-page">
      <h6 className="title">Mantenimiento de Estados de Cuenta</h6>
      <div className="estado-content">
        <EstadoDropDowns></EstadoDropDowns>
      </div>
      <EstadoButtons></EstadoButtons>

      <Modal
        isOpen={showModal}
        title="Consultar"
        onClose={() => {
          setShowModal(false);
        }}
        onAcept={() => {}}
      >
        <div style={{ display: "flex", gap: "1rem" }}>
          <DropDown
            opts={DropDownBancosOptions}
            change={() => {}}
            click={() => {}}
            placeholder="Bancos"
            selectedValue="BBVA"
            disabled={false}
            style={{ width: "180px" }}
          ></DropDown>
          <DropDown
            opts={DropDownCuentasOptions}
            change={() => {}}
            click={() => {}}
            placeholder="Cuentas"
            selectedValue="1234"
            disabled={false}
            style={{ width: "180px" }}
          ></DropDown>
        </div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            gap: "1rem",
            marginTop: "1rem",
          }}
        >
          <DatePicker
            id="fecha-inicial"
            label="FECHA INICIAL"
            value={fechaInicial}
            onChange={(e) => setFechaInicial(e.target.value)}
            disabled={false}
            className=""
            style={{ width: "180px" }}
          ></DatePicker>
          <DatePicker
            id="fecha-final"
            label="FECHA FINAL"
            value={fechaFinal}
            onChange={(e) => setFechaFinal(e.target.value)}
            disabled={false}
            className=""
          ></DatePicker>
        </div>
        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "1rem",
            marginTop: "1.51rem",
          }}
        >
          <Button
            text="Aceptar"
            style={{}}
            funcionC={() => {
              navigate("consulta");
              setShowModal(false);
            }}
          ></Button>
          <Button
            text="Salir"
            style={{}}
            funcionC={() => {
              setShowModal(false);
            }}
          ></Button>
        </div>
      </Modal>
    </div>
  );
};

export default Estado;
