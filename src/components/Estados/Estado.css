.estado-page {
  display: flex;
  flex-direction: column;
  align-items: center;

  font-family: "Inter", "sans-serifS";
  width: 100%;
}
.estado-content {
  max-width: 900px;
  display: flex;

  align-items: center;
  width: 100%;
}
.title {
  text-align: center;

  gap: 1rem;
  font-size: 2rem;
  font-weight: bold;
  width: 100%;
  margin-top: 3rem;
}
.dropdown-container {
  display: flex;
  align-items: center;
  justify-content: center;

  justify-content: center;
  align-items: center;
  padding-right: 5rem !important;

  flex-direction: row !important;
}
.combo-select-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding-top: 1rem;
  padding-bottom: 1.5rem;
  text-wrap: nowrap;
}

.form-inline-bancos {
  display: flex;
  align-items: center;
  flex-direction: row;
}
.form-inline-cuentas {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.form-inline-File {
  display: flex;
  justify-content: space-between;
  align-items: center;

  padding-left: 1rem;
  margin-top: 1.5rem;
  gap: 2rem;
  flex-wrap: wrap;
}
.form-inline-File label {
  font-weight: bold;
  white-space: nowrap;
  align-items: start;
  margin-left: 0;
  padding-left: 0;
}

.file-label-row {
  display: flex;

  justify-content: center;
  align-items: center;
  padding-left: -50vh;

  gap: 1rem;
}
.check-container {
  justify-content: center;
  align-items: start;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem 2rem;

  display: flex;
  margin-top: 1rem;
  flex-direction: column;
  gap: 0.9rem;
}
.oculto {
  display: none;
}
