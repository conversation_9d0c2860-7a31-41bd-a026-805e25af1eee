import { FC, useEffect, useState } from "react";
import {
  initialPostApi,
  useGet,
  usePost,
  UsePostProps,
} from "../../../hooks/useFetch";
import Table from "../../MetlifeComponents/Table/Table";
import {
  TableData,
  TableResponse,
} from "../../MetlifeComponents/Table/table-types";
import MLDCReactLoadingIndicator from "@metlife/mldc-react-loading-indicator";
import { mockColumns, mockData } from "../Consultar/consultaMockData";
import DropDown from "../../MetlifeComponents/DropDown/DropDown";
import "./Procesar.css";
import Button from "../../MetlifeComponents/Button/Button";
import DatePicker from "../../MetlifeComponents/DatePicker/DatePicker";

type ProcesaProps = {};
const DropDownBancosHandleClick = () => {};
const DropDownCuentasHandleClick = () => {};
const DropDownBancosHandleOnChange = (value: string) => {
  const [BancoSeleccionado, setBancoSeleccionado] = useState("");
};
const DropDownCuentasHandleOnchange = () => {};
const handleUpload = (file: File) => {};
const DropDownBancosStyle = {
  maxWidth: "300px",
  width: "100%",
  marginTop: "1rem",
};
const DropDownCuentasStyle = {
  maxWidth: "300px",
  width: "100%",
  marginTop: "0.5rem",
};

const DropDownBancosOptions = [
  {
    ariaLabel: "BBVA",
    label: "BBVA",
    value: "BBVA",
  },
  {
    ariaLabel: "City",
    label: "City",
    value: "City",
  },
  {
    ariaLabel: "HSBC",
    label: "HSBC",
    value: "HSBC",
  },
  {
    ariaLabel: "JPMorgan",
    label: "JPMorgan",
    value: "JPMorgan",
  },
];
const optionsR = [
  {
    label: "Formato Tradicional",
    value: "tradicional",
  },
  {
    label: "MT490",
    value: "MT490",
  },
];
const radioStyle = {};
const DropDownCuentasOptions = [
  {
    ariaLabel: "1234",
    label: "1234",
    value: "1234",
  },
  {
    ariaLabel: "2323232323",
    label: "2323232323",
    value: "2323232323",
  },
  {
    ariaLabel: "4545465464",
    label: "545465464",
    value: "545465464",
  },
];

const Procesa: FC<ProcesaProps> = () => {
  const ButtonProcesarStyle = {
    display: "flex",
  };
  const ButtonConsultarStyle = {
    display: "flex",
  };
  const ButtonSalirStyle = {
    display: "flex",
  };
  const handleOnClickConsultar = () => {
    setShowModal(true);
  };
  const handleOnClickSalir = () => {};
  const handleOnClickProcesar = () => {};
  const [showModal, setShowModal] = useState(false);
  const [fechaInicial, setFechaInicial] = useState("");
  const [fechaFinal, setFechaFinal] = useState("");
  const [callPostAPI, setCallPostAPI] = useState<UsePostProps>(initialPostApi);

  const { result: responsePost } = usePost(callPostAPI);
  const { result, loading, error, refetch } = useGet<TableResponse>({
    url: "https://api.example.com/",
    call: false, //true TODO: until have the BE
  });
  console.log({ result, loading, error });

  const onSaveCompania = (newElement: TableData) => {
    setCallPostAPI({
      url: "https://api.example.com/",
      call: true,
      data: newElement,
    });
  };

  const onEditCompania = (editElement: TableData) => {
    setCallPostAPI({
      url: `https://api.example.com/${editElement?.id}`,
      call: true,
      method: "PUT",
      data: editElement,
    });
  };

  const onDeleteCompania = (elementId: number) => {
    setCallPostAPI({
      url: `https://api.example.com/${elementId}`,
      call: true,
      method: "DELETE",
    });
  };

  useEffect(() => {
    if (responsePost != null) {
      refetch();
      setCallPostAPI(initialPostApi);
    }
  }, [responsePost, refetch]);

  if (loading)
    return (
      <MLDCReactLoadingIndicator id="Loading" className="loading-wrapper" />
    );
  if (error) return <>{`Error: ${error.message}`}</>;

  return (
    <div className="container">
      <div className="title-container">
        <h1 className="title">MANTENIMIENTO DE ESTADOS</h1>
      </div>
      <div className="met-dropdowns">
        <div className="form-inline-bancos">
          <label htmlFor=" bancos"> Banco:</label>
          <DropDown
            change={DropDownBancosHandleOnChange}
            click={DropDownBancosHandleClick}
            opts={DropDownBancosOptions}
            style={DropDownBancosStyle}
            placeholder="Seleccione Banco"
            disabled={false}
          ></DropDown>
        </div>
        <div className="form-inline-cuentas">
          <label htmlFor="cuentas"> Cuenta:</label>
          <DropDown
            change={DropDownCuentasHandleOnchange}
            click={DropDownBancosHandleClick}
            style={DropDownCuentasStyle}
            opts={DropDownCuentasOptions}
            placeholder="Seleccione Cuenta"
            disabled={false}
          ></DropDown>
        </div>
      </div>

      <Table
        id="consulta"
        // data={result?.data ?? []}
        // columns={result?.columns ?? []}
        data={mockData}
        columns={mockColumns}
        onSave={onSaveCompania}
        onEdit={onEditCompania}
        onDelete={onDeleteCompania}
      />
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          gap: "1rem",
          marginTop: "-25px",
        }}
      >
        <DatePicker
          id="fecha-inicial"
          label="FECHA INICIAL"
          value={fechaInicial}
          onChange={(e) => setFechaInicial(e.target.value)}
          disabled={false}
          className=""
          style={{ width: "180px" }}
        ></DatePicker>
        <DatePicker
          id="fecha-final"
          label="FECHA FINAL"
          value={fechaFinal}
          onChange={(e) => setFechaFinal(e.target.value)}
          disabled={false}
          className=""
        ></DatePicker>
      </div>
      <div className="form-inline-button">
        <Button
          text="Procesar"
          style={ButtonProcesarStyle}
          funcionC={handleOnClickProcesar}
        ></Button>
        <Button
          text="Consultar"
          style={ButtonProcesarStyle}
          funcionC={handleOnClickConsultar}
        ></Button>
        <Button
          text="Salir"
          style={ButtonSalirStyle}
          funcionC={handleOnClickSalir}
        ></Button>
      </div>
    </div>
  );
};

export default Procesa;
