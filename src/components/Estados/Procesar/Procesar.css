.title-conatiner {
  display: flex;
  padding: 1rem;
  align-items: center;
  justify-content: center;
}
.title {
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}
.met-dropdowns {
  display: flex;
  flex-direction: row;
  border: 1px red;
  gap: 1rem;
  margin-bottom: 2rem;

  align-items: flex-end;
}
estado-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 2rem;
}
.estado-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  max-width: 600px;
  width: 100%;
  padding-left: 1rem;
  gap: 1.5rem;
}
.title {
  text-align: center;
  font-size: 1.8rem;
  margin-bottom: 2rem;
  width: 100%;
}
.dropdown-container {
  padding-top: 4rem;
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1;
  align-items: flex-end;
  margin-left: 20vh;
}
.combo-select-container {
  margin-top: -50px;
  padding-top: 0;
}
.form-inline-bancos {
  padding-top: 2rem;
  display: flex;

  align-items: center;
  gap: 1rem;
}
.form-inline-bancos label {
  font-weight: bold;
  min-width: 60px;
}
.form-inline-cuentas {
  padding-top: 2rem;
  display: flex;

  align-items: center;
  gap: 1rem;
}
.form-inline-cuentas label {
  font-weight: bold;
  min-width: 60px;
}
.form-inline-File {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  padding-left: 1rem;
  margin-top: 1.5rem;
  gap: 2rem;
  flex-wrap: wrap;
  width: 100%;
}
.form-inline-File label {
  font-weight: bold;
  white-space: nowrap;
  align-items: start;
  margin-left: 0;
  padding-left: 0;
}
.col-uploader {
  flex: 1;
  min-width: 300px;
}
.form-inline-button {
  display: flex;
  gap: 1rem;
  flex-shrink: 0;
  justify-content: start;
  margin-top: auto;
  padding-top: 2rem;
  padding-bottom: 3rem;
  padding-left: 50vh;
}
.file-label-row {
  display: flex;

  justify-content: center;
  width: 100%;
  gap: 1rem;
}
.check-container {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem 2rem;
  display: flex;
  margin-top: 1rem;
  flex-direction: column;
  gap: 0.9rem;
  margin-left: 60vh;
  max-width: 400px;
}
