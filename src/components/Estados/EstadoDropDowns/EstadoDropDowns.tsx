import React, { useState } from "react";
import "./EstadoDropDowns.css";
import DropDown from "../../MetlifeComponents/DropDown/DropDown";
import FileUploader from "../../FileUploader/FileUploader";
type DropDownRowProps = {};
const EstadoDropDowns: React.FC<DropDownRowProps> = ({}) => {
  const DropDownBancosHandleClick = () => {};
  const DropDownCuentasHandleClick = () => {};
  const DropDownBancosHandleOnChange = (value: string) => {
    setBancoSeleccionado(value);
  };
  const DropDownCuentasHandleOnchange = () => {};
  const handleUpload = (file: File) => {};
  const DropDownBancosStyle = {
    width: "225.29px",
    height: "50px",
    marginTop: "1rem",
  };
  const DropDownCuentasStyle = {
    width: "225.29x",
    height: "50px",
    marginTop: "0.5rem",
  };

  const DropDownBancosOptions = [
    {
      ariaLabel: "BBVA",
      label: "BBVA",
      value: "BBVA",
    },
    {
      ariaLabel: "City",
      label: "City",
      value: "City",
    },
    {
      ariaLabel: "HSBC",
      label: "HSBC",
      value: "HSBC",
    },
    {
      ariaLabel: "JPMorgan",
      label: "JPMorgan",
      value: "JPMorgan",
    },
  ];
  const [BancoSeleccionado, setBancoSeleccionado] = useState("");
  const FileOnUpload = () => {};
  const DropDownCuentasOptions = [
    {
      ariaLabel: "1234",
      label: "1234",
      value: "1234",
    },
    {
      ariaLabel: "2323232323",
      label: "2323232323",
      value: "2323232323",
    },
    {
      ariaLabel: "4545465464",
      label: "545465464",
      value: "545465464",
    },
  ];
  return (
    <div className="main">
      <div className="bloque-superior">
        <div className="dropdown-container">
          <div className="form-inline-bancos">
            <label htmlFor=" bancos"> Banco:</label>
            <DropDown
              change={DropDownBancosHandleOnChange}
              click={DropDownBancosHandleClick}
              opts={DropDownBancosOptions}
              style={DropDownBancosStyle}
              placeholder="Seleccione Banco"
              disabled={false}
            ></DropDown>
          </div>
          <div className="form-inline-cuentas">
            <label htmlFor="cuentas"> Cuenta:</label>
            <DropDown
              change={DropDownCuentasHandleOnchange}
              click={DropDownBancosHandleClick}
              style={DropDownCuentasStyle}
              opts={DropDownCuentasOptions}
              placeholder="Seleccione Cuenta"
              disabled={!BancoSeleccionado}
            ></DropDown>
          </div>
        </div>
      </div>

      <div className="file-upload-wrapper">
        <FileUploader
          id="file-uploader"
          onupload={handleUpload}
          label="Cargar Estado de Cuenta"
          allowedTypes={[".csv", ".pdf", ".txt"]}
          maxSizeMb={50}
          text="Cargar estado de cuenta"
        ></FileUploader>
      </div>
    </div>
  );
};
export default EstadoDropDowns;
