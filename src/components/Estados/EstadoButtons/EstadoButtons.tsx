import React from "react";
import "./EstadoButtons.css";
import Button from "../../MetlifeComponents/Button/Button";
type EstadoButtonsProps = { buttons?: React.ReactNode[] };

const EstadoButtons: React.FC<EstadoButtonsProps> = ({ buttons }) => {
  const handleOnClickConsultar = () => {
    // setShowModal(true);
  };
  const handleOnClickSalir = () => {};
  const handleOnClickProcesar = () => {
    // navigate("procesa");
  };
  return (
    <div className="buttons-wrapper">
      <div
        className="buttons-container"
        style={{ display: "flex", justifyContent: "center" }}
      >
        <Button text="Procesar" funcionC={handleOnClickProcesar}></Button>,
        <Button text="Consultar" funcionC={handleOnClickConsultar}></Button>,
        <Button text="Salir" funcionC={handleOnClickSalir}></Button>
      </div>
    </div>
  );
};
export default EstadoButtons;
