import { TableInputType } from "../../enums";
import { TableColumn, TableData } from "../MetlifeComponents/Table/table-types";

const mockColumns: TableColumn[] = [
  {
    id: "table-head-1",
    label: "ID",
    accessor: "id",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-2",
    label: "COMPAÑÍA",
    accessor: "company",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-3",
    label: "ÁREA",
    accessor: "area",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-4",
    label: "CONCEPTO",
    accessor: "concept",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-5",
    label: "DESCRIPCIÓN",
    accessor: "description",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-6",
    label: "USUARIO",
    accessor: "user",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-7",
    label: "FECHA",
    accessor: "date",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.DATE,
  },
];

const mockData: TableData[] = [
  {
    id: "1",
    company: "Metlife Mexico",
    area: "B1-SGM-Foraneo",
    concept: "PU80",
    description: "Pago de siniestros de gastos medicos.",
    user: "e369874",
    date: "10/02/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "2",
    company: "Metlife Seguros",
    area: "A2-SGV-Local",
    concept: "PU90",
    description: "Pago de siniestros de vida individual.",
    user: "e452198",
    date: "15/03/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-2",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "3",
    company: "Metlife Pensiones",
    area: "C3-SGA-Nacional",
    concept: "PU70",
    description: "Pago de pensiones mensuales.",
    user: "e781245",
    date: "22/04/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-3",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "4",
    company: "Metlife Mexico",
    area: "D4-SGD-Regional",
    concept: "PU60",
    description: "Pago de comisiones a agentes.",
    user: "e523987",
    date: "05/05/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-4",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "5",
    company: "Metlife Servicios",
    area: "E5-SGE-Central",
    concept: "PU50",
    description: "Pago de proveedores administrativos.",
    user: "e647123",
    date: "18/06/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-5",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "6",
    company: "Metlife Inversiones",
    area: "F6-SGF-Corporativo",
    concept: "PU40",
    description: "Pago de rendimientos a inversionistas.",
    user: "e789456",
    date: "30/07/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-6",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "7",
    company: "Metlife Mexico",
    area: "G7-SGG-Metropolitano",
    concept: "PU30",
    description: "Pago de nómina a empleados.",
    user: "e321654",
    date: "12/08/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-7",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "8",
    company: "Metlife Seguros",
    area: "H8-SGH-Fronterizo",
    concept: "PU25",
    description: "Pago de siniestros de accidentes personales.",
    user: "e456789",
    date: "05/09/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-8",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "9",
    company: "Metlife Mexico",
    area: "I9-SGI-Sureste",
    concept: "PU20",
    description: "Pago de beneficios por fallecimiento.",
    user: "e987654",
    date: "18/09/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-9",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "10",
    company: "Metlife Pensiones",
    area: "J10-SGJ-Noreste",
    concept: "PU15",
    description: "Pago de pensiones por invalidez.",
    user: "e123789",
    date: "25/09/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-10",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "11",
    company: "Metlife Servicios",
    area: "K11-SGK-Occidente",
    concept: "PU10",
    description: "Pago de servicios de consultoría externa.",
    user: "e654321",
    date: "03/10/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-11",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "12",
    company: "Metlife Mexico",
    area: "L12-SGL-Centro",
    concept: "PU05",
    description: "Pago de reembolsos a asegurados.",
    user: "e789123",
    date: "12/10/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-12",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "13",
    company: "Metlife Inversiones",
    area: "M13-SGM-Nacional",
    concept: "PU95",
    description: "Pago de dividendos a accionistas.",
    user: "e456123",
    date: "20/10/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-13",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "14",
    company: "Metlife Seguros",
    area: "N14-SGN-Regional",
    concept: "PU85",
    description: "Pago de comisiones por renovación de pólizas.",
    user: "e321987",
    date: "28/10/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-14",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "15",
    company: "Metlife Mexico",
    area: "O15-SGO-Corporativo",
    concept: "PU75",
    description: "Pago de impuestos federales.",
    user: "e654987",
    date: "05/11/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-15",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "16",
    company: "Metlife Pensiones",
    area: "P16-SGP-Metropolitano",
    concept: "PU65",
    description: "Pago de pensiones por viudez.",
    user: "e789456",
    date: "15/11/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-16",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "17",
    company: "Metlife Servicios",
    area: "Q17-SGQ-Foraneo",
    concept: "PU55",
    description: "Pago de servicios de mantenimiento.",
    user: "e123456",
    date: "22/11/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-17",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "18",
    company: "Metlife Mexico",
    area: "R18-SGR-Local",
    concept: "PU45",
    description: "Pago de servicios públicos.",
    user: "e987321",
    date: "30/11/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-18",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "19",
    company: "Metlife Inversiones",
    area: "S19-SGS-Nacional",
    concept: "PU35",
    description: "Pago de intereses por préstamos.",
    user: "e456789",
    date: "08/12/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-19",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "20",
    company: "Metlife Seguros",
    area: "T20-SGT-Regional",
    concept: "PU28",
    description: "Pago de siniestros de daños materiales.",
    user: "e789123",
    date: "15/12/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-20",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "21",
    company: "Metlife Mexico",
    area: "U21-SGU-Sureste",
    concept: "PU22",
    description: "Pago de gastos de viaje corporativos.",
    user: "e321654",
    date: "22/12/2023",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-21",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "22",
    company: "Metlife Pensiones",
    area: "V22-SGV-Noreste",
    concept: "PU18",
    description: "Pago de pensiones por orfandad.",
    user: "e654321",
    date: "05/01/2024",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-22",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "23",
    company: "Metlife Servicios",
    area: "W23-SGW-Occidente",
    concept: "PU12",
    description: "Pago de servicios de seguridad.",
    user: "e987654",
    date: "12/01/2024",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-23",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "24",
    company: "Metlife Mexico",
    area: "X24-SGX-Centro",
    concept: "PU08",
    description: "Pago de arrendamiento de oficinas.",
    user: "e123789",
    date: "20/01/2024",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-24",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "25",
    company: "Metlife Inversiones",
    area: "Y25-SGY-Nacional",
    concept: "PU98",
    description: "Pago de comisiones por administración de fondos.",
    user: "e456123",
    date: "28/01/2024",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-25",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "26",
    company: "Metlife Seguros",
    area: "Z26-SGZ-Regional",
    concept: "PU92",
    description: "Pago de siniestros de vida grupo.",
    user: "e789456",
    date: "05/02/2024",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-26",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "27",
    company: "Metlife Mexico",
    area: "A27-SGA-Corporativo",
    concept: "PU82",
    description: "Pago de impuestos estatales.",
    user: "e321987",
    date: "15/02/2024",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-27",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
];

export { mockData, mockColumns };
