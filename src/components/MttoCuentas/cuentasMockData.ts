import { Facebook } from "react-bootstrap-icons";
import { TableInputType } from "../../enums";
import { TableColumn, TableData } from "../MetlifeComponents/Table/table-types";

const mockColumns: TableColumn[] = [
  {
    id: "table-head-1",
    label: "ID",
    accessor: "id",
    isLink: false,
    sortable: true,
    hidden: true,
    editable: false,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-2",
    label: "MONEDA",
    accessor: "TCEF_CVE_MONEDA",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-3",
    label: "NUMERO CUENTA",
    accessor: "TCEF_NUM_CUENTA",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-4",
    label: "CUARTO NIVEL CONTABLE",
    accessor: "TCTA_AUX_CONT",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-5",
    label: "NUMERO CHEQUE",
    accessor: "TCTA_NUM_CHEQUE",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-6",
    label: "SUCURSAL",
    accessor: "TCTA_ID_SUCURSAL",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-7",
    label: "DIGITO VERIFICADOR",
    accessor: "TCTA_CAL_DIGITO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-8",
    label: "PROTECCION CHEQUE",
    accessor: "TCTA_PROT_CKE",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-9",
    label: "USUARIO CREO",
    accessor: "TCTA_USER_CREO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-10",
    label: "FECHA CREO",
    accessor: "TCTA_FEC_CREO",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.DATE,
  },
  {
    id: "table-head-11",
    label: "USUARIO MOD",
    accessor: "TCTA_USER_MOD",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-12",
    label: "FECHA MOD",
    accessor: "TCTA_FEC_MOD",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    type: TableInputType.DATE,
  },
];

const mockData: TableData[] = [
  {
    ID: "1",
    TCEF_CVE_MONEDA: "MN",
    TCEF_NUM_CUENTA: "2338008",
    TCTA_AUX_CONT: "423",
    TCTA_NUM_CHEQUE: "7878534",
    TCTA_ID_SUCURSAL: "519990029",
    TCTA_CAL_DIGITO: "0023",
    TCTA_PROT_CKE: "S",
    TCTA_USER_CREO: "USER",
    TCTA_FEC_CREO: "01/04/2025",
    TCTA_USER_MOD: null,
    TCTA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "1",
  },
  {
    ID: "2",
    TCEF_CVE_MONEDA: "MN",
    TCEF_NUM_CUENTA: "0188014191",
    TCTA_AUX_CONT: "436",
    TCTA_NUM_CHEQUE: "6373",
    TCTA_ID_SUCURSAL: "511150179",
    TCTA_CAL_DIGITO: "",
    TCTA_PROT_CKE: "",
    TCTA_USER_CREO: "USER",
    TCTA_FEC_CREO: "01/04/2025",
    TCTA_USER_MOD: "USER_MOD",
    TCTA_FEC_MOD: "15/04/2025",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "2",
  },
  {
    ID: "3",
    TCEF_CVE_MONEDA: "MN",
    TCEF_NUM_CUENTA: "2338016",
    TCTA_AUX_CONT: "444",
    TCTA_NUM_CHEQUE: "",
    TCTA_ID_SUCURSAL: "",
    TCTA_CAL_DIGITO: "",
    TCTA_PROT_CKE: "",
    TCTA_USER_CREO: "USER",
    TCTA_FEC_CREO: "01/04/2025",
    TCTA_USER_MOD: null,
    TCTA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "3",
  },
  /*
  
  
  
  
  
  
  
  */
];

export { mockData, mockColumns };
