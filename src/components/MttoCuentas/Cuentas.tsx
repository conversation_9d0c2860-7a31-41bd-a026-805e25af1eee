import { FC } from "react";
import Table from "../MetlifeComponents/Table/Table";
import { mockColumns, mockData } from "./cuentasMockData";

type CompaniaProps = {};

const Compania: FC<CompaniaProps> = () => {
  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE CUENTAS BANCARIAS</h1>
      <Table
        id="companias"
        data={mockData}
        columns={mockColumns}
        filters={["TCEF_NUM_CUENTA"]}
      />
    </div>
  );
};

export default Compania;
