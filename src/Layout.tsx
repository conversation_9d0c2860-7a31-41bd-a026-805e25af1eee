import { FC } from "react";
import buildInfo from "./buildInfo";
import Sidebar from "./components/MetlifeComponents/Sidebar/Sidebar";
import MLDCReactHeroBanner from "@metlife/mldc-react-hero-banner";

type LayoutProps = {
  children: React.ReactNode;
};

const Layout: FC<LayoutProps> = ({ children }) => {
  return (
    <div className="layout">
      <div className="left_wrapper">
        <Sidebar />
      </div>
      <div className="right_wrapper">
        <div className="top_wrapper">
          <MLDCReactHeroBanner
            id="home-page"
            className="home-page override-hero"
            bannerType="secondary-page"
            headingText="TESORERIA"
            eyebrow={`Version: ${buildInfo.buildVersion}`}
            subheader="DESARROLLO"
          />
        </div>
        <div className="body_wrapper">{children}</div>
      </div>
    </div>
  );
};

export default Layout;
