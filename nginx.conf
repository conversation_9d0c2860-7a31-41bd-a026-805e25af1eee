server {
    listen 80;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html?$query_string;
    }

    location = /favicon.ico {
        log_not_found off;
    }

    location = /robots.txt {
        log_not_found off;
    }

    location ~ /\.(ht|svn|git) {
        deny all;
    }

    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}