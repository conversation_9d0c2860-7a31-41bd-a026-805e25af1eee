# File: /build-templates/build/dev-build.yml
steps:

- task: NodeTool@0
  displayName: 'Use Node $(NODE_VERSION)'
  inputs:
    versionSpec: $(NODE_VERSION)

- task: Npm@1
  displayName: 'npm install'
  inputs:
    verbose: false
    customCommand: 'ci'

- task: Npm@1
  displayName: 'npm build'
  inputs:
    command: custom
    verbose: false
    customCommand: 'run build'

# - template: templates/ml-azure-sonar.yml@mxtreasury-devops
#   parameters:
#     projectkey: $(sonarprojectkey)
#     projectname: $(sonarprojectname)
#     sourcefolder: $(sonarSource)
#     coveragepath: $(javascriptlcovreportPaths)
#     sonarlanguage: JavaScript
#     coverageexclusions: src/logger/**/*.js, src/client/services/*.js, src/client/index.js, src/client/utils/adobeAnalytics.js, src/logger/, src/server/**/*.*, src/client/**/useStyles.js, src/client/pages/Quotation/Schema/*.*
#     exclusions: src/client/_tests_/**/*.js, src/server/__tests__/**/*.js, src/server/**/*.*, src/mockdata/*.*, src/client/pages/Quotation/Schema/*.*

# - template: templates/ml-azure-veracode.yml@mxtreasury-devops
#   parameters:
#     projectkey: $(VERACODE_PROJECT)
#     sandboxname: '14316_treasury-ui-$(Build.SourceBranchName)'
#     sourcefolder: 'src'

- task: PowerShell@2
  displayName: 'Create ZIP file of proyect'
  inputs:
    targetType: 'inline'
    script: |
      Write-Host "##[debug] Content of build"
      Get-ChildItem -Recurse -Path "$(System.DefaultWorkingDirectory)/build"

      $buildDir = "$(System.DefaultWorkingDirectory)/build"
      $webConfig = "$(System.DefaultWorkingDirectory)/web.config"
      $zipOutput = "$(Build.ArtifactStagingDirectory)/deploy.zip"

      # Validations
      if (-not (Test-Path -Path $buildDir -PathType Container)) {
        Write-Host "##[error]The directory $buildDir don't exists!"
        Get-ChildItem -Recurse -Path "$(System.DefaultWorkingDirectory)"
        exit 1
      }

      if (-not (Test-Path -Path $webConfig -PathType Leaf)) {
        Write-Host "##[error]web.config file not found at $webConfig"
        exit 1
      }

      New-Item -ItemType Directory -Path "$(Build.ArtifactStagingDirectory)" -Force | Out-Null

      Write-Host "##[command]Preparing files to ZIP..."

      # Using .NET for create ZIP file
      Add-Type -AssemblyName System.IO.Compression
      Add-Type -AssemblyName System.IO.Compression.FileSystem
      
      try {
        [System.IO.Compression.ZipFile]::CreateFromDirectory("$buildDir", $zipOutput, [System.IO.Compression.CompressionLevel]::Optimal, $false)

        $zip = [System.IO.Compression.ZipFile]::Open($zipOutput, [System.IO.Compression.ZipArchiveMode]::Update)
        [System.IO.Compression.ZipFileExtensions]::CreateEntryFromFile($zip, $webConfig, "web.config")
        $zip.Dispose()

        Write-Host "##[section]ZIP successfully created at: $zipOutput"

        # Verify zip file
        Write-Host "##[debug]ZIP contents:"
        $zip = [System.IO.Compression.ZipFile]::OpenRead($zipOutput)
        $zip.Entries.FullName | Sort-Object
        $zip.Dispose()
      }
      catch {
        Write-Host "##[error]Error creating ZIP: $_"
        exit 1
      }

- task: PublishPipelineArtifact@1
  displayName: 'Publish ZIP Artifact'
  inputs:
    targetPath: '$(Build.ArtifactStagingDirectory)/deploy.zip'
    artifact: $(ARTIFACT_NAME)