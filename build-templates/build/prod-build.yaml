# ADB_WEB
# File: templates/build/prod-build.yml
steps:

- task: SonarSource.sonarqube.15B84CA1-B62F-4A2A-A403-89B77A063157.SonarQubePrepare@4
  displayName: 'Prepare analysis on SonarQube'
  inputs:
    SonarQube: SonarQube
    scannerMode: CLI
    configMode: manual
    cliProjectKey: 'com.metlife.saga.adb-web'
    cliProjectName: 'adb-web'
    extraProperties: |
     # Additional properties that will be passed to the scanner, 
     # Put one key=value per line, example:
     # sonar.exclusions=**/*.bin
     sonar.coverage.exclusions=**/*
  enabled: false  
     

- task: qetza.replacetokens.replacetokens-task.replacetokens@4
  displayName: 'Replace image footer'
  inputs:
    rootDirectory: k8s
    targetFiles: '$(System.DefaultWorkingDirectory)/server/cloud/Footer.tsx'
    tokenPattern: doublebraces

- script: |
   cp $(System.DefaultWorkingDirectory)/server/cloud/Footer.tsx $(System.DefaultWorkingDirectory)/src/layouts/admin/components/Footer/
   
  displayName: 'Replace Footer to Release'

- task: Gradle@2
  displayName: 'gradle build'
  inputs:
    tasks: 'createDockerfile -Pargs=cloud -Psso=Dynamic'
    publishJUnitResults: false

- task: SonarSource.sonarqube.6D01813A-9589-4B15-8491-8164AEB38055.SonarQubeAnalyze@4
  displayName: 'Run Code Analysis'
  enabled: false

- task: SonarSource.sonarqube.291ed61f-1ee4-45d3-b1b0-bf822d9095ef.SonarQubePublish@4
  displayName: 'Publish Quality Gate Result'
  enabled: false

- task: SimondeLang.sonar-buildbreaker.sonar-buildbreaker.sonar-buildbreaker@8
  displayName: 'Break build on quality gate failure'
  inputs:
    SonarQube: SonarQube
  enabled: false

- task: Docker@2
  displayName: 'build for DTR'
  inputs:
    containerRegistry: '$(SIGA_CICD_PROD_DTR_CONTAINER_REGISTRY)'
    repository: '$(SIGA_CICD_PROD_DTR_REPOS_ADB_WEB)'
    command: build
    Dockerfile: Dockerfile
    buildContext: .
    tags: '$(Build.SourceBranchName)-$(Build.BuildId)'
    arguments: '--build-arg BUSER=$(ArtifactoryUser) --build-arg BPASS=$(ArtifactoryUserPassword) --build-arg BRANCH=$(Build.SourceBranchName) --build-arg BUILD=$(Build.BuildNumber)'

- task: Docker@2
  displayName: 'build for ACR'
  inputs:
    containerRegistry: '$(SIGA_CICD_PROD_ACR_CONTAINER_REGISTRY)'
    repository: '$(SIGA_CICD_PROD_ACR_REPOS_ADB_WEB)'
    command: build
    Dockerfile: Dockerfile
    buildContext: .
    tags: '$(Build.SourceBranchName)-$(Build.BuildId)'
    arguments: '--build-arg BUSER=$(ArtifactoryUser) --build-arg BPASS=$(ArtifactoryUserPassword) --build-arg BRANCH=$(Build.SourceBranchName) --build-arg BUILD=$(Build.BuildNumber)'
  enabled: false

- task: Docker@2
  displayName: 'Docker Push to DTR'
  inputs:
    containerRegistry: '$(SIGA_CICD_PROD_DTR_CONTAINER_REGISTRY)'
    repository: '$(SIGA_CICD_PROD_DTR_REPOS_ADB_WEB)'
    command: push
    tags: '$(Build.SourceBranchName)-$(Build.BuildId)'

- task: Docker@2
  displayName: 'Docker Push to ACR'
  inputs:
    containerRegistry: '$(SIGA_CICD_PROD_ACR_CONTAINER_REGISTRY)'
    repository: '$(SIGA_CICD_PROD_ACR_REPOS_ADB_WEB)'
    command: push
    tags: '$(Build.SourceBranchName)-$(Build.BuildId)'
  enabled: false

- task: JFrog.jfrog-artifactory-vsts-extension.artifactory-generic-upload.ArtifactoryGenericUpload@2
  displayName: 'Artifactory Manifiests Upload'
  inputs:
    artifactoryService: Artifactory
    fileSpec: |
     {
       "files": [
         {
           "pattern": "k8s/*.yaml",
           "target": "libs-snapshot-local/adb-web-ci/k8s/$(Build.BuildNumber)/"
         }
       ]
     }
    collectBuildInfo: true

- task: JFrog.jfrog-artifactory-vsts-extension.artifactory-publish-build-info.ArtifactoryPublishBuildInfo@1
  displayName: 'Artifactory Publish Build Info'
  inputs:
    artifactoryService: Artifactory


