# PIP DEV Deploy pipeline
# File: /build-templates/deploy/deploy-dev.yml

steps:
  - task: DownloadPipelineArtifact@2
    displayName: 'Download Zip Artifact'
    inputs:
      artifactName: $(ARTIFACT_NAME)
      downloadPath: '$(Build.ArtifactStagingDirectory)'
      
  - task: AzureWebApp@1
    displayName: 'Deploy ZIP to App Service'
    inputs:
      azureSubscription: $(SERVICE_CONNECTION_ARM)
      appType: 'webApp'
      appName: $(APP_SERVICE_NAME)
      resourceGroupName: $(RG_NAME)
      package: '$(Build.ArtifactStagingDirectory)/deploy.zip'
      deploymentMethod: 'zipDeploy'