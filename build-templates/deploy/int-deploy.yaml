
steps:
- task: JFrog.jfrog-artifactory-vsts-extension.artifactory-generic-download.ArtifactoryGenericDownload@3
  displayName: 'Artifactory Manifests Download'
  inputs:
    connection: Artifactory
    fileSpec: |
     {
       "files": [
         {
           "pattern": "libs-snapshot-local/adb-web-ci/k8s/$(Build.BuildNumber)/deployment-k8s.yaml",
           "target": "$(System.DefaultWorkingDirectory)/_adb-web/k8s/"
         },
        {
           "pattern": "libs-snapshot-local/adb-web-ci/k8s/$(Build.BuildNumber)/service.yaml",
           "target": "$(System.DefaultWorkingDirectory)/_adb-web/k8s/"
         },
        {
           "pattern": "libs-snapshot-local/adb-web-ci/k8s/$(Build.BuildNumber)/podautoescaler.yaml",
           "target": "$(System.DefaultWorkingDirectory)/_adb-web/k8s/"
         }
       ]
     }
    collectBuildInfo: true

- script: 'ls -a'
  workingDirectory: '$(System.DefaultWorkingDirectory)/_adb-web/k8s/adb-web-ci/k8s/$(Build.BuildNumber)'
  displayName: 'Command Line Script'

- task: qetza.replacetokens.replacetokens-task.replacetokens@3
  displayName: 'Replace image name'
  inputs:
    rootDirectory: '$(System.DefaultWorkingDirectory)/_adb-web/k8s/adb-web-ci/k8s/$(Build.BuildNumber)'
    targetFiles: 'deployment-k8s.yaml'
    tokenPrefix: '{{'
    tokenSuffix: '}}'

- script: 'cat deployment-k8s.yaml'
   
  workingDirectory: '$(System.DefaultWorkingDirectory)/_adb-web/k8s/adb-web-ci/k8s/$(Build.BuildNumber)'
  displayName: 'show siga deployment'
  
- task: KubectlInstaller@0
  displayName: 'Install Kubectl cli'
  
- task: AzureCLI@2
  displayName: 'kubelogin - install'
  inputs:
    azureSubscription: '$(SIGA_CICD_INT_AKS_SUBSCRIPTION)'
    scriptType: bash
    scriptLocation: inlineScript
    inlineScript: |
     # get kubelogin
     wget https://github.com/Azure/kubelogin/releases/download/$(SIGA_CICD_INT_KUBELOGIN_VERSION)/kubelogin-linux-amd64.zip
     unzip kubelogin-linux-amd64.zip -d $(Agent.TempDirectory)
     export PATH=$(Agent.TempDirectory)/bin/linux_amd64:$PATH
     $(Agent.TempDirectory)/bin/linux_amd64/kubelogin convert-kubeconfig -l azurecli --token-cache-dir $(Agent.TempDirectory)
     kubelogin --version

- task: AzureCLI@2
  displayName: 'Azure AKS get credentials'
  inputs:
    azureSubscription: '$(SIGA_CICD_INT_AKS_SUBSCRIPTION)'
    scriptType: bash
    scriptLocation: inlineScript
    inlineScript: 'az aks get-credentials --resource-group $(SIGA_CICD_INT_AKS_RESOURCE_GROUP) --name $(SIGA_CICD_INT_AKS_NAME) --file $(Agent.TempDirectory)/config-$(System.JobId)'

- task: AzureCLI@2
  displayName: 'Azure AKS apply manifests'
  inputs:
    azureSubscription: '$(SIGA_CICD_INT_AKS_SUBSCRIPTION)'
    scriptType: bash
    scriptLocation: inlineScript
    inlineScript: |
     export KUBECONFIG=$(Agent.TempDirectory)/config-$(System.JobId)
     $(Agent.TempDirectory)/bin/linux_amd64/kubelogin convert-kubeconfig -l azurecli --token-cache-dir $(Agent.TempDirectory)
     export PATH=$(Agent.TempDirectory)/bin/linux_amd64:$PATH
     kubectl apply -f $(System.DefaultWorkingDirectory)/_adb-web/k8s/adb-web-ci/k8s/$(Build.BuildNumber)/deployment-k8s.yaml -n $(SIGA_CICD_INT_NAMESPACE_FRONT)
     kubectl apply -f $(System.DefaultWorkingDirectory)/_adb-web/k8s/adb-web-ci/k8s/$(Build.BuildNumber)/service.yaml -n $(SIGA_CICD_INT_NAMESPACE_FRONT)

- task: AzureCLI@2
  displayName: 'Azure AKS deploy verification'
  inputs:
    azureSubscription: '$(SIGA_CICD_INT_AKS_SUBSCRIPTION)'
    scriptType: bash
    scriptLocation: inlineScript
    inlineScript: |
     export KUBECONFIG=$(Agent.TempDirectory)/config-$(System.JobId)
     $(Agent.TempDirectory)/bin/linux_amd64/kubelogin convert-kubeconfig -l azurecli --token-cache-dir $(Agent.TempDirectory)
     export PATH=$(Agent.TempDirectory)/bin/linux_amd64:$PATH
     sleep 5
     kubectl get pod -n  $(SIGA_CICD_INT_NAMESPACE_FRONT)
     sleep 15
     kubectl get pod -n $(SIGA_CICD_INT_NAMESPACE_FRONT)

- script: |
   export KUBECONFIG=$(Agent.TempDirectory)/config-$(System.JobId)
   kubectl apply -f $(System.DefaultWorkingDirectory)/_adb-web/k8s/adb-web-ci/k8s/$(Build.BuildNumber)/ -n $(SIGA_CICD_INT_NAMESPACE_FRONT)
   displayName: 'k8s apply manifests'
  enabled: false
  
- script: |
   export KUBECONFIG=$(Agent.TempDirectory)/config-$(System.JobId)
   sleep 5
   kubectl get pod -n $(SIGA_CICD_INT_NAMESPACE_FRONT)
   sleep 45
   kubectl get pod -n $(SIGA_CICD_INT_NAMESPACE_FRONT)
   displayName: 'k8s deploy verification'
  enabled: false
