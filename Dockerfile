FROM --platform=linux/amd64 node:23-alpine AS builder

WORKDIR /app

RUN npm config set registry https://jfrog-artifactory.metlife.com/artifactory/api/npm/remote-npm/

# 1. Copy configuration files
COPY package.json yarn.lock* package-lock.json* ./

# 2. Install dependencies
RUN --mount=type=secret,id=npmrc,target=/root/.npmrc \
    npm ci --verbose

# 3. Copy project files
COPY . .

# 4. Build application on develop mode with memory max for Node
RUN NODE_OPTIONS="--max-old-space-size=4096" npm run build

RUN cd build && ls -al

# Final step
# FROM node:23-alpine

# WORKDIR /app

# # 5. Copy necessary files to builder
# COPY --from=builder /app/package.json /app/package-lock.json ./

# RUN --mount=type=secret,id=npmrc,target=/root/.npmrc \
#     npm ci --omit=dev && \
#     rm -f /root/.npmrc

# COPY --from=builder /app/build ./build
# COPY --from=builder /app/public ./public
# COPY --from=builder /app/node_modules ./node_modules

# # 6. Environment configuration optimized
# ENV NODE_ENV=development
# ENV PORT=80
# ENV NODE_OPTIONS="--enable-source-maps"

# # 7. Health check
# #HEALTHCHECK --interval=30s --timeout=3s \
# #    CMD curl -f http://localhost:${PORT}/health || exit 1

# # 8. Expose and Run
# EXPOSE ${PORT}
# USER node
# CMD [ "node", "dist/server.js" ]
FROM mcr.microsoft.com/windows/servercore:ltsc2019

RUN powershell -Command \
    Install-WindowsFeature -Name Web-Static-Content -IncludeManagementTools; \
    New-Item -Path C:\inetpub\wwwroot -ItemType Directory -Force

WORKDIR C:\inetpub\wwwroot
COPY --from=builder /app/build .
COPY web.config .

EXPOSE 80
