// Test script to verify the TransferList filtering logic

// Simulate the sampleAssignments data structure
const sampleAssignments = {
  'I-admin': [
    {
      id: "assigned-i-admin-1",
      name: "Administración Completa - Ingresos",
      module: "I",
      children: [
        { id: "assigned-i-admin-1-1", name: "Mantenimiento de Compañías", module: "I" },
        { id: "assigned-i-admin-1-2", name: "Mantenimiento de Usuarios", module: "I" },
      ],
    },
  ],
  'E-admin': [
    {
      id: "assigned-e-admin-1",
      name: "Administración Completa - Egresos",
      module: "E",
      children: [
        { id: "assigned-e-admin-1-1", name: "Mantenimiento de Proveedores", module: "E" },
        { id: "assigned-e-admin-1-2", name: "Mantenimiento de Conceptos de Pago", module: "E" },
      ],
    },
  ],
};

// Simulate the getInitialRightItems function
const getInitialRightItems = (module, role) => {
  console.log('🔍 Getting initial right items for:', { module, role });
  console.log('📊 Available keys in sampleAssignments:', Object.keys(sampleAssignments));

  if (!role || role === "") {
    // Default items when no role is selected
    const defaultItems = module === "I" ? [
      {
        id: "default-i",
        name: "Menús Básicos de Ingresos",
        module: "I",
        children: [
          { id: "default-i-1", name: "Consulta de Movimientos", module: "I" },
          { id: "default-i-2", name: "Reportes Básicos", module: "I" },
        ],
      },
    ] : [
      {
        id: "default-e",
        name: "Menús Básicos de Egresos",
        module: "E",
        children: [
          { id: "default-e-1", name: "Consulta de Pagos", module: "E" },
          { id: "default-e-2", name: "Reportes Básicos", module: "E" },
        ],
      },
    ];
    console.log('✅ Returning default items for module:', module, defaultItems);
    return defaultItems;
  }

  const key = `${module}-${role}`;
  const result = sampleAssignments[key] || [];
  console.log('🔑 Key:', key, '📋 Result:', result);
  console.log('🎯 Result length:', result.length);
  if (result.length > 0) {
    console.log('📝 First item module:', result[0].module);
  }
  return result;
};

// Test cases
console.log('\n=== TEST 1: Ingresos with admin role ===');
const test1 = getInitialRightItems('I', 'admin');
console.log('Result:', test1);

console.log('\n=== TEST 2: Egresos with admin role ===');
const test2 = getInitialRightItems('E', 'admin');
console.log('Result:', test2);

console.log('\n=== TEST 3: Ingresos with no role ===');
const test3 = getInitialRightItems('I', '');
console.log('Result:', test3);

console.log('\n=== TEST 4: Egresos with no role ===');
const test4 = getInitialRightItems('E', '');
console.log('Result:', test4);
